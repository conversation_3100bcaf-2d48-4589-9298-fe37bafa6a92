name: Woiden VPS 多账户自动续期

on:
  schedule:
    - cron: '0 0 */2 * *'  # 每2天运行一次
  workflow_dispatch:  # 允许手动触发

jobs:
  renew:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.9'
        
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install selenium webdriver-manager telethon pyyaml
        
    - name: 创建必要的目录
      run: |
        mkdir -p sessions
        mkdir -p screenshots
        mkdir -p logs
        touch screenshots/placeholder.png
        touch logs/placeholder.log
        
    - name: 安装Chrome和驱动
      run: |
        # 安装Chrome
        wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
        sudo apt-get update
        sudo apt-get install -y google-chrome-stable
        
        # 安装运行环境
        sudo apt-get install -y xvfb
        
        # 显示Chrome版本
        google-chrome --version
        
    - name: 检查NopeCHA扩展
      run: |
        echo "使用内置的NopeCHA扩展..."
        ls -la chrome_extensions/nopecha || echo "警告：内置扩展目录不存在"
        
    - name: 生成配置文件
      env:
        TELEGRAM_API_ID_1: ${{ secrets.TELEGRAM_API_ID_1 }}
        TELEGRAM_API_HASH_1: ${{ secrets.TELEGRAM_API_HASH_1 }}
        TELEGRAM_SESSION_1: ${{ secrets.TELEGRAM_SESSION_1 }}
        TELEGRAM_API_ID_2: ${{ secrets.TELEGRAM_API_ID_2 }}
        TELEGRAM_API_HASH_2: ${{ secrets.TELEGRAM_API_HASH_2 }}
        TELEGRAM_SESSION_2: ${{ secrets.TELEGRAM_SESSION_2 }}
        NOPECHA_API_KEY: ${{ secrets.NOPECHA_API_KEY }}
      run: |
        python github_actions_config_generator.py

        # 确认配置文件内容（隐藏敏感信息）
        echo "配置文件已生成，检查结构..."
        python -c "import yaml; config=yaml.safe_load(open('config.yaml')); print(f'账户数量: {len(config[\"accounts\"])}'); print('全局配置已设置')"
        
    - name: 修改Python脚本以适应GitHub Actions环境
      run: |
        cat > playwright_support.py << EOL
        # 该文件仅作为占位符，实际使用selenium
        def setup_browser(config):
            """兼容函数"""
            return None, None
        EOL
        
    - name: 运行多账户续期脚本
      run: |
        # 添加调试信息
        echo "运行环境信息:"
        python --version
        google-chrome --version
        ls -la
        
        # 确保目录存在且有写入权限
        sudo chmod -R 777 screenshots logs sessions chrome_extensions
        
        # 设置虚拟显示器
        export DISPLAY=:99
        sudo Xvfb $DISPLAY -screen 0 1280x1024x24 &
        sleep 3
        
        # 配置WebDriver Manager环境变量
        export PATH=$PATH:$(python -m webdriver_manager.chrome)

        # 设置GitHub Actions环境变量
        export GITHUB_ACTIONS=true

        # 运行脚本并捕获输出到日志
        python multi_account_renewer.py 2>&1 | tee logs/renew.log
      
    - name: 检查日志和截图目录
      if: always()
      run: |
        echo "检查日志和截图目录..."
        ls -la
        ls -la logs/ || echo "logs目录不存在或为空"
        ls -la screenshots/ || echo "screenshots目录不存在或为空"
        
        # 如果目录为空，确保有测试文件
        if [ ! -f logs/renew.log ]; then
          echo "创建renew.log占位文件..."
          echo "运行失败，请检查GitHub Actions日志" > logs/renew.log
        fi
        
        if [ ! -f screenshots/test.png ]; then
          echo "创建test.png占位文件..."
          echo "占位图片" > screenshots/test.png
        fi
        
    - name: 上传截图和日志
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: screenshots-and-logs
        path: |
          screenshots/
          logs/
        retention-days: 5 