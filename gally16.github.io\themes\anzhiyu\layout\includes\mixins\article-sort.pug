mixin articleSort(posts, current)
  .article-sort
    - var year
    - let modifiedUrl
    - posts.each(function (article, post_index) {
      - let tempYear = date(article.date, 'YYYY')
      - let no_cover = article.cover === false || !theme.cover.archives_enable ? 'no-article-cover' : ''
      - let title = article.title || _p('no_title')
      - let pageThumbnailSuffix = theme.pageThumbnailSuffix
      if (pageThumbnailSuffix && theme.pageThumbnailSuffix.startsWith("!") && article.cover && article.cover.includes("!"))
        - let imageUrl = article.cover.substring(0, article.cover.indexOf("!"))
        - modifiedUrl = imageUrl + pageThumbnailSuffix
      else
        - modifiedUrl = article.cover
      if tempYear !== year
        - year = tempYear
        .article-sort-item.year
          span= year
      .article-sort-item(class=no_cover)
        if article.cover && theme.cover.archives_enable
          a.article-sort-item-img(href=url_for(article.path) title=title)
            img(src=url_for(modifiedUrl) alt=title onerror=`this.onerror=null;this.src='${url_for(theme.error_img.post_page)}'`)
        .article-sort-item-info
          a.article-sort-item-title(href=url_for(article.path) title=title)= title
          span.article-sort-item-index= (current - 1) * config.per_page + post_index + 1
          .article-meta-wrap
            if (theme.post_meta.page.tags && article.tags.data.length > 0)
              span.article-sort-item-tags
                each item, index in article.tags.data
                  a(href=url_for(item.path) tabindex="-1").article-meta__tags 
                    span 
                      i.anzhiyufont.anzhiyu-icon-hashtag
                      =item.name
            .article-sort-item-time
              i.anzhiyufont.anzhiyu-icon-calendar-alt
              time.post-meta-date-created(datetime=date_xml(article.date) title=_p('post.created') + ' ' + full_date(article.date))= date(article.date, config.date_format)
    - })