#!/usr/bin/env python3
"""
验证Bot Token配置脚本
检查项目中的Bot Token配置是否正确
"""

import os
import re
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_github_actions_workflow():
    """检查GitHub Actions工作流配置"""
    logger.info("检查GitHub Actions工作流配置...")
    
    workflow_file = Path(".github/workflows/multi_vps_renew.yml")
    if not workflow_file.exists():
        logger.error("❌ GitHub Actions工作流文件不存在")
        return False
    
    with open(workflow_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含Bot Token环境变量
    required_tokens = ['TELEGRAM_BOT_TOKEN1', 'TELEGRAM_BOT_TOKEN2']
    missing_tokens = []
    
    for token in required_tokens:
        if f"${{{{ secrets.{token} }}}}" not in content:
            missing_tokens.append(token)
    
    if missing_tokens:
        logger.error(f"❌ GitHub Actions工作流缺少Bot Token配置: {missing_tokens}")
        return False
    else:
        logger.info("✅ GitHub Actions工作流Bot Token配置正确")
        return True

def check_config_generator():
    """检查配置生成器"""
    logger.info("检查配置生成器...")
    
    config_gen_file = Path("github_actions_config_generator.py")
    if not config_gen_file.exists():
        logger.error("❌ 配置生成器文件不存在")
        return False
    
    with open(config_gen_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否正确读取Bot Token环境变量
    if "TELEGRAM_BOT_TOKEN" not in content:
        logger.error("❌ 配置生成器未包含Bot Token处理")
        return False
    
    # 检查是否优先使用Bot Token
    if "bot_token = os.environ.get(f'TELEGRAM_BOT_TOKEN{i}')" not in content:
        logger.error("❌ 配置生成器未正确读取Bot Token")
        return False
    
    logger.info("✅ 配置生成器Bot Token配置正确")
    return True

def check_main_program():
    """检查主程序"""
    logger.info("检查主程序...")
    
    main_file = Path("multi_account_renewer.py")
    if not main_file.exists():
        logger.error("❌ 主程序文件不存在")
        return False
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否支持Bot Token
    if "self.bot_token" not in content:
        logger.error("❌ 主程序未包含Bot Token支持")
        return False
    
    # 检查是否优先使用Bot Token
    if "if self.bot_token:" not in content:
        logger.error("❌ 主程序未优先使用Bot Token")
        return False
    
    # 检查是否正确使用Bot Token启动
    if "await self.telegram_client.start(bot_token=self.bot_token)" not in content:
        logger.error("❌ 主程序未正确使用Bot Token启动")
        return False
    
    logger.info("✅ 主程序Bot Token配置正确")
    return True

def check_documentation():
    """检查文档"""
    logger.info("检查文档...")
    
    # 检查README
    readme_file = Path("README.md")
    if readme_file.exists():
        with open(readme_file, 'r', encoding='utf-8') as f:
            readme_content = f.read()
        
        if "TELEGRAM_BOT_TOKEN" in readme_content:
            logger.info("✅ README包含Bot Token说明")
        else:
            logger.warning("⚠️  README未包含Bot Token说明")
    
    # 检查Bot Token指南
    bot_guide_file = Path("BOT_TOKEN_GUIDE.md")
    if bot_guide_file.exists():
        logger.info("✅ Bot Token指南文件存在")
    else:
        logger.warning("⚠️  Bot Token指南文件不存在")
    
    return True

def simulate_environment_test():
    """模拟环境变量测试"""
    logger.info("模拟环境变量测试...")
    
    # 设置测试环境变量
    test_env = {
        'TELEGRAM_API_ID_1': '12345',
        'TELEGRAM_API_HASH_1': 'test_hash_1',
        'TELEGRAM_BOT_TOKEN1': '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11',
        'TELEGRAM_API_ID_2': '67890',
        'TELEGRAM_API_HASH_2': 'test_hash_2',
        'TELEGRAM_BOT_TOKEN2': '789012:XYZ-ABC5678mnOpq-rst90X3w2v456fg22',
    }
    
    # 临时设置环境变量
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    try:
        # 测试配置生成
        from github_actions_config_generator import generate_config
        success = generate_config()
        
        if success:
            logger.info("✅ 环境变量测试通过")
            
            # 检查生成的配置文件
            import yaml
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            accounts = config.get('accounts', [])
            for account in accounts:
                if account['telegram'].get('bot_token'):
                    logger.info(f"✅ {account['name']}: Bot Token配置正确")
                else:
                    logger.error(f"❌ {account['name']}: Bot Token配置失败")
                    return False
            
            return True
        else:
            logger.error("❌ 环境变量测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 环境变量测试异常: {str(e)}")
        return False
    finally:
        # 恢复原始环境变量
        for key, value in original_env.items():
            if value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = value

def main():
    """主函数"""
    logger.info("验证Bot Token配置")
    logger.info("=" * 50)
    
    checks = [
        ("GitHub Actions工作流", check_github_actions_workflow),
        ("配置生成器", check_config_generator),
        ("主程序", check_main_program),
        ("文档", check_documentation),
        ("环境变量测试", simulate_environment_test),
    ]
    
    results = []
    for name, check_func in checks:
        logger.info(f"\n检查 {name}...")
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            logger.error(f"❌ {name} 检查失败: {str(e)}")
            results.append((name, False))
    
    logger.info("\n" + "=" * 50)
    logger.info("检查结果:")
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{name}: {status}")
        if not result:
            all_passed = False
    
    logger.info("\n" + "=" * 50)
    if all_passed:
        logger.info("🎉 所有检查通过！Bot Token配置正确")
        logger.info("现在可以在GitHub Actions中使用Bot Token了")
        return 0
    else:
        logger.error("❌ 部分检查失败，请修复上述问题")
        return 1

if __name__ == "__main__":
    exit(main())
