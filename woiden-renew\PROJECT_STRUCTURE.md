# 📁 项目结构说明

## 核心文件

### 🚀 主程序
- **`multi_account_renewer.py`** - 多账户VPS续期主程序
- **`multi_scheduler.py`** - 定时任务调度器（本地使用）

### ⚙️ 配置和设置
- **`github_actions_config_generator.py`** - GitHub Actions配置生成器
- **`config.yaml`** - 配置文件模板
- **`requirements.txt`** - Python依赖包列表

### 🛠️ 工具脚本
- **`quick_start.py`** - 快速启动菜单（推荐新手使用）
- **`generate_telegram_session.py`** - Telegram会话数据生成器

### 📚 文档
- **`README.md`** - 项目主要说明文档
- **`SETUP_GUIDE.md`** - 详细设置指南
- **`BOT_TOKEN_GUIDE.md`** - Bot Token创建指南

### 🔧 扩展和配置
- **`chrome_extensions/nopecha/`** - NopeCHA验证码自动解决扩展
- **`.github/workflows/`** - GitHub Actions工作流配置

## 使用方式

### 🎯 快速开始
```bash
python quick_start.py
```

### 🤖 GitHub Actions
1. Fork仓库
2. 设置Secrets
3. 启用Actions

### 🖥️ 本地运行
```bash
# 单次运行
python multi_account_renewer.py

# 定时运行
python multi_scheduler.py
```

## 文件用途

| 文件 | 用途 | 必需性 |
|------|------|--------|
| `multi_account_renewer.py` | 核心续期程序 | ✅ 必需 |
| `github_actions_config_generator.py` | GitHub Actions配置 | ✅ 必需 |
| `quick_start.py` | 新手友好的启动菜单 | 🔵 推荐 |
| `generate_telegram_session.py` | 生成Telegram会话 | 🔵 推荐 |
| `multi_scheduler.py` | 本地定时任务 | 🟡 可选 |
| `config.yaml` | 配置模板 | 🟡 可选 |

## 已删除的文件

以下文件已被删除，因为它们是过时的单账户版本或重复功能：

- ❌ `auto_renew.py` - 旧版单账户程序
- ❌ `auto_renew_with_nopecha.py` - 旧版单账户程序
- ❌ `scheduler.py` - 旧版调度器
- ❌ `scheduler_nopecha.py` - 旧版调度器
- ❌ `github_action_renew.py` - 旧版GitHub Actions程序
- ❌ `test_*.py` - 测试文件
- ❌ `*.log` - 日志文件
- ❌ `*.session` - 会话文件

## 目录说明

```
woiden-renew/
├── 📄 核心程序文件
├── 📚 文档文件
├── 🔧 chrome_extensions/     # Chrome扩展
├── 🤖 .github/workflows/     # GitHub Actions配置
├── 📝 logs/                  # 运行日志（自动生成）
├── 📸 screenshots/           # 截图（自动生成）
└── 🔐 sessions/              # Telegram会话（自动生成）
```

现在项目结构更加清晰，只保留了必要的文件！
