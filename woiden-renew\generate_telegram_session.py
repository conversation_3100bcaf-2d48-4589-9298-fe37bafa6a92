#!/usr/bin/env python3
"""
Telegram会话生成器
用于在本地环境中生成Telegram会话数据，然后可以在GitHub Actions中使用
"""

import os
import base64
import asyncio
import logging
from pathlib import Path
from telethon import TelegramClient

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def generate_session(account_name, api_id, api_hash):
    """生成Telegram会话数据"""
    
    if not api_id or not api_hash:
        logger.error(f"账户 {account_name}: API ID 和 API Hash 不能为空")
        return None
    
    session_file = f"sessions/{account_name}_session.session"
    
    # 创建sessions目录
    Path("sessions").mkdir(exist_ok=True)
    
    logger.info(f"开始为账户 {account_name} 生成会话数据...")
    logger.info("请准备好您的手机，接收验证码")
    
    try:
        # 创建Telegram客户端
        client = TelegramClient(session_file, api_id, api_hash)
        
        # 启动客户端（这会提示输入电话号码和验证码）
        await client.start()
        
        logger.info(f"账户 {account_name} 登录成功！")
        
        # 测试连接
        me = await client.get_me()
        logger.info(f"已登录用户: {me.first_name} {me.last_name or ''} (@{me.username or 'N/A'})")
        
        # 关闭客户端
        await client.disconnect()
        
        # 读取会话文件并转换为base64
        if os.path.exists(session_file):
            with open(session_file, 'rb') as f:
                session_data = f.read()
            
            session_base64 = base64.b64encode(session_data).decode('utf-8')
            
            logger.info(f"账户 {account_name} 的会话数据已生成")
            logger.info("=" * 50)
            logger.info(f"请将以下内容保存为GitHub Secret: TELEGRAM_SESSION_{account_name.upper()}")
            logger.info("=" * 50)
            print(session_base64)
            logger.info("=" * 50)
            
            return session_base64
        else:
            logger.error(f"会话文件 {session_file} 不存在")
            return None
            
    except Exception as e:
        logger.error(f"生成会话数据失败: {str(e)}")
        return None

async def main():
    """主函数"""
    logger.info("Telegram会话数据生成器")
    logger.info("=" * 50)
    logger.info("此工具将帮助您生成Telegram会话数据，用于GitHub Actions自动化")
    logger.info("请确保您有以下信息：")
    logger.info("1. Telegram API ID")
    logger.info("2. Telegram API Hash")
    logger.info("3. 能够接收验证码的手机")
    logger.info("=" * 50)
    
    # 获取用户输入
    accounts = []
    
    while True:
        account_name = input(f"\n请输入账户名称 (例如: account1, account2) [回车结束]: ").strip()
        if not account_name:
            break
            
        api_id = input(f"请输入 {account_name} 的 API ID: ").strip()
        api_hash = input(f"请输入 {account_name} 的 API Hash: ").strip()
        
        if api_id and api_hash:
            accounts.append((account_name, api_id, api_hash))
        else:
            logger.warning("API ID 和 API Hash 不能为空，跳过此账户")
    
    if not accounts:
        logger.error("没有有效的账户配置")
        return
    
    # 生成会话数据
    sessions = {}
    for account_name, api_id, api_hash in accounts:
        logger.info(f"\n处理账户: {account_name}")
        session_data = await generate_session(account_name, api_id, api_hash)
        if session_data:
            sessions[account_name] = session_data
    
    # 输出总结
    logger.info("\n" + "=" * 50)
    logger.info("会话数据生成完成！")
    logger.info("=" * 50)
    
    if sessions:
        logger.info("请在GitHub仓库的 Settings -> Secrets and variables -> Actions 中添加以下Secrets:")
        for account_name in sessions:
            secret_name = f"TELEGRAM_SESSION_{account_name.upper()}"
            logger.info(f"- {secret_name}")
        
        logger.info("\n同时还需要添加以下API凭据:")
        for account_name, api_id, api_hash in accounts:
            if account_name in sessions:
                logger.info(f"- TELEGRAM_API_ID_{account_name.upper()}: {api_id}")
                logger.info(f"- TELEGRAM_API_HASH_{account_name.upper()}: {api_hash}")
    else:
        logger.error("没有成功生成任何会话数据")

if __name__ == "__main__":
    asyncio.run(main())
