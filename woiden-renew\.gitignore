# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
logs/
renew_log.txt
multi_renew_log.txt

# Screenshots
screenshots/
*.png
*.jpg
*.jpeg

# Sessions and temporary files
sessions/
*.session
temp/
tmp/

# Configuration files with sensitive data
config_local.yaml
.env

# OS
.DS_Store
Thumbs.db

# Chrome extensions (keep the directory structure but ignore downloads)
chrome_extensions/*/crx/
chrome_extensions/*/downloads/

# Test files
test_*.py
*_test.py
