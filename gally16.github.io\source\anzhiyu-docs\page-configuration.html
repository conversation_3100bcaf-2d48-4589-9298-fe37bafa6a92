<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.61">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <link rel="icon" href="/docs/images/c192.png"><title>页面配置 | 安知鱼主题指南</title><meta name="description" content="安知鱼主题页面配置">
    <link rel="preload" href="/anzhiyu-docs/assets/style-47b50212.css" as="style"><link rel="stylesheet" href="/anzhiyu-docs/assets/style-47b50212.css">
    <link rel="modulepreload" href="/anzhiyu-docs/assets/app-18744df2.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/framework-2fd1fcd7.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/page-configuration.html-cc2f4bd1.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/page-configuration.html-89a1d5ab.js"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-e7463a10.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/quick-start.html-ad03cacb.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-0505f6c4.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration1.html-50f88905.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-d3638ef1.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-7a1b5d87.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration4.html-e8a4bf32.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-f9875e7b.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-f69e6992.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/quick-start.html-5dcde832.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-2a772ecf.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration1.html-f7afdcb5.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-e0d0931a.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-74290865.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration4.html-919542bb.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-bbf580d2.js" as="script">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container"><!--[--><header class="navbar"><div class="toggle-sidebar-button" title="toggle sidebar" aria-expanded="false" role="button" tabindex="0"><div class="icon" aria-hidden="true"><span></span><span></span><span></span></div></div><span><a href="/anzhiyu-docs/" class=""><img class="logo" src="/anzhiyu-docs/./images/c192.png" alt="安知鱼主题指南"><span class="site-name can-hide">安知鱼主题指南</span></a></span><div class="navbar-items-wrapper" style=""><!--[--><!--]--><nav class="navbar-items can-hide"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><button class="toggle-color-mode-button" title="toggle color mode"><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button><form class="search-box" role="search"><input type="search" placeholder="Search" autocomplete="off" spellcheck="false" value><!----></form></div></header><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar"><nav class="navbar-items"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><ul class="sidebar-items"><!--[--><li><a href="/anzhiyu-docs/" class="sidebar-item sidebar-heading" aria-label="简介"><!--[--><!--]--> 简介 <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/quick-start.html" class="sidebar-item sidebar-heading" aria-label="快速上手"><!--[--><!--]--> 快速上手 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html" class="router-link-active router-link-exact-active router-link-active sidebar-item sidebar-heading active" aria-label="页面配置"><!--[--><!--]--> 页面配置 <!--[--><!--]--></a><ul style="" class="sidebar-item-children"><!--[--><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#front-matter-的基本认识" class="router-link-active router-link-exact-active sidebar-item" aria-label="Front-matter 的基本认识"><!--[--><!--]--> Front-matter 的基本认识 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#标签页" class="router-link-active router-link-exact-active sidebar-item" aria-label="标签页"><!--[--><!--]--> 标签页 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#分类页" class="router-link-active router-link-exact-active sidebar-item" aria-label="分类页"><!--[--><!--]--> 分类页 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#首页即刻说说页面配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="首页即刻说说页面配置"><!--[--><!--]--> 首页即刻说说页面配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#友情链接配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="友情链接配置"><!--[--><!--]--> 友情链接配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#关于页面配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="关于页面配置"><!--[--><!--]--> 关于页面配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#配置相册页面" class="router-link-active router-link-exact-active sidebar-item" aria-label="配置相册页面"><!--[--><!--]--> 配置相册页面 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#朋友圈页面配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="朋友圈页面配置"><!--[--><!--]--> 朋友圈页面配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#音乐馆页配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="音乐馆页配置"><!--[--><!--]--> 音乐馆页配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#_404-页面" class="router-link-active router-link-exact-active sidebar-item" aria-label="404 页面"><!--[--><!--]--> 404 页面 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#追番页面" class="router-link-active router-link-exact-active sidebar-item" aria-label="追番页面"><!--[--><!--]--> 追番页面 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#留言板页面" class="router-link-active router-link-exact-active sidebar-item" aria-label="留言板页面"><!--[--><!--]--> 留言板页面 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/page-configuration.html#我的装备页面" class="router-link-active router-link-exact-active sidebar-item" aria-label="我的装备页面"><!--[--><!--]--> 我的装备页面 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/anzhiyu-docs/site-configuration1.html" class="sidebar-item sidebar-heading" aria-label="站点配置(一)"><!--[--><!--]--> 站点配置(一) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration2.html" class="sidebar-item sidebar-heading" aria-label="站点配置(二)"><!--[--><!--]--> 站点配置(二) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration3.html" class="sidebar-item sidebar-heading" aria-label="站点配置(三)"><!--[--><!--]--> 站点配置(三) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration4.html" class="sidebar-item sidebar-heading" aria-label="站点配置(四)"><!--[--><!--]--> 站点配置(四) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/reward-list.html" class="sidebar-item sidebar-heading" aria-label="赞赏名单"><!--[--><!--]--> 赞赏名单 <!--[--><!--]--></a><!----></li><!--]--></ul><!--[--><!--]--></aside><!--]--><!--[--><main class="page"><!--[--><!--]--><div class="theme-default-content"><!--[--><!--]--><div><div class="hint-container warning"><p class="hint-container-title">警告</p><p>本教程更新于 2023 年 7 月 5 日，教程的内容针对最新的 anzhiyu 主题(如果你是旧版本，教程会有出入，请留意) 🐟 安知鱼 已经更新到 <a href="https://github.com/anzhiyu-c/hexo-theme-anzhiyu/releases/tag/1.4.0" target="_blank" rel="noopener noreferrer">1.4.0<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><h2 id="front-matter-的基本认识" tabindex="-1"><a class="header-anchor" href="#front-matter-的基本认识" aria-hidden="true">#</a> Front-matter 的基本认识</h2><p><code>Front-matter</code> 是 <code>markdown</code> 文件最上方以 <code>---</code> 分隔的区域，用于指定个别档案的变数。</p><p>其中又分为两种</p><ol><li>Page Front-matter 用于页面配置</li><li>Post Front-matter 用于文章页配置</li></ol><div class="hint-container info"><p class="hint-container-title">提示</p><p>如果标注可选的参数，可根据自己需要添加，不用全部都写在 markdown 里</p></div><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-31-0" aria-selected="true">Page Front-matter</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-31-1" aria-selected="false">Post Front-matter</button></div><!--[--><div class="tab-item active" id="tab-31-0" role="tabpanel" aria-expanded="true"><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span>
<span class="token key atrule">date</span><span class="token punctuation">:</span>
<span class="token key atrule">updated</span><span class="token punctuation">:</span>
<span class="token key atrule">type</span><span class="token punctuation">:</span>
<span class="token key atrule">comments</span><span class="token punctuation">:</span>
<span class="token key atrule">description</span><span class="token punctuation">:</span>
<span class="token key atrule">keywords</span><span class="token punctuation">:</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span>
<span class="token key atrule">mathjax</span><span class="token punctuation">:</span>
<span class="token key atrule">katex</span><span class="token punctuation">:</span>
<span class="token key atrule">aside</span><span class="token punctuation">:</span>
<span class="token key atrule">aplayer</span><span class="token punctuation">:</span>
<span class="token key atrule">highlight_shrink</span><span class="token punctuation">:</span>
type<span class="token punctuation">:</span></span>
<span class="token punctuation">---</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>写法</th><th>解释</th></tr></thead><tbody><tr><td>title</td><td>【必需】页面标题</td></tr><tr><td>date</td><td>【必需】页面创建日期</td></tr><tr><td>type</td><td>【必需】标签、分类、关于、音乐馆、友情链接、相册、相册详情、朋友圈、即刻页面需要配置</td></tr><tr><td>updated</td><td>【可选】页面更新日期</td></tr><tr><td>description</td><td>【可选】页面描述</td></tr><tr><td>keywords</td><td>【可选】页面关键字</td></tr><tr><td>comments</td><td>【可选】显示页面评论模块(默认 true)</td></tr><tr><td>top_img</td><td>【可选】页面顶部图片</td></tr><tr><td>mathjax</td><td>【可选】显示 mathjax(当设置 mathjax 的 per_page: false 时，才需要配置，默认 false)</td></tr><tr><td>katex</td><td>【可选】显示 katex(当设置 katex 的 per_page: false 时，才需要配置，默认 false)</td></tr><tr><td>aside</td><td>【可选】显示侧边栏 (默认 true)</td></tr><tr><td>aplayer</td><td>【可选】在需要的页面加载 aplayer 的 js 和 css,请参考文章下面的音乐 配置</td></tr><tr><td>highlight_shrink</td><td>【可选】配置代码框是否展开(true/false)(默认为设置中 highlight_shrink 的配置)</td></tr></tbody></table></div><div class="tab-item" id="tab-31-1" role="tabpanel" aria-expanded="false"><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span>
<span class="token key atrule">date</span><span class="token punctuation">:</span>
<span class="token key atrule">updated</span><span class="token punctuation">:</span>
<span class="token key atrule">tags</span><span class="token punctuation">:</span>
<span class="token key atrule">categories</span><span class="token punctuation">:</span>
<span class="token key atrule">keywords</span><span class="token punctuation">:</span>
<span class="token key atrule">description</span><span class="token punctuation">:</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span>
<span class="token key atrule">comments</span><span class="token punctuation">:</span>
<span class="token key atrule">cover</span><span class="token punctuation">:</span>
<span class="token key atrule">toc</span><span class="token punctuation">:</span>
<span class="token key atrule">toc_number</span><span class="token punctuation">:</span>
<span class="token key atrule">toc_style_simple</span><span class="token punctuation">:</span>
<span class="token key atrule">copyright</span><span class="token punctuation">:</span>
<span class="token key atrule">copyright_author</span><span class="token punctuation">:</span>
<span class="token key atrule">copyright_author_href</span><span class="token punctuation">:</span>
<span class="token key atrule">copyright_url</span><span class="token punctuation">:</span>
<span class="token key atrule">copyright_info</span><span class="token punctuation">:</span>
<span class="token key atrule">mathjax</span><span class="token punctuation">:</span>
<span class="token key atrule">katex</span><span class="token punctuation">:</span>
<span class="token key atrule">aplayer</span><span class="token punctuation">:</span>
<span class="token key atrule">highlight_shrink</span><span class="token punctuation">:</span>
<span class="token key atrule">aside</span><span class="token punctuation">:</span>
<span class="token key atrule">swiper_index</span><span class="token punctuation">:</span> <span class="token number">1</span>
<span class="token key atrule">top_group_index</span><span class="token punctuation">:</span> <span class="token number">1</span>
<span class="token key atrule">background</span><span class="token punctuation">:</span> <span class="token string">&quot;#fff&quot;</span></span>
<span class="token punctuation">---</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>写法</th><th>解释</th></tr></thead><tbody><tr><td>title</td><td>【必需】文章标题</td></tr><tr><td>date</td><td>【必需】文章创建日期</td></tr><tr><td>updated</td><td>【可选】文章更新日期</td></tr><tr><td>tags</td><td>【可选】文章标签</td></tr><tr><td>categories</td><td>【可选】文章分类</td></tr><tr><td>keywords</td><td>【可选】文章关键字</td></tr><tr><td>description</td><td>【可选】文章描述</td></tr><tr><td>top_img</td><td>【可选】文章顶部图片</td></tr><tr><td>cover</td><td>【可选】文章缩略图(如果没有设置 top_img,文章页顶部将显示缩略图，可设为 false/图片地址/留空)</td></tr><tr><td>comments</td><td>【可选】显示文章评论模块(默认 true)</td></tr><tr><td>toc</td><td>【可选】显示文章 TOC(默认为设置中 toc 的 enable 配置)</td></tr><tr><td>toc_number</td><td>【可选】显示 toc_number(默认为设置中 toc 的 number 配置)</td></tr><tr><td>toc_style_simple</td><td>【可选】显示 toc 简洁模式</td></tr><tr><td>copyright</td><td>【可选】显示文章版权模块(默认为设置中 post_copyright 的 enable 配置)</td></tr><tr><td>copyright_author</td><td>【可选】文章版权模块的<code>文章作者</code></td></tr><tr><td>copyright_author_href</td><td>【可选】文章版权模块的<code>文章作者</code>链接</td></tr><tr><td>copyright_url</td><td>【可选】文章版权模块的<code>文章链接</code>链接</td></tr><tr><td>copyright_info</td><td>【可选】文章版权模块的版权声明文字</td></tr><tr><td>mathjax</td><td>【可选】显示 mathjax(当设置 mathjax 的 per_page: false 时，才需要配置，默认 false)</td></tr><tr><td>katex</td><td>【可选】显示 katex(当设置 katex 的 per_page: false 时，才需要配置，默认 false)</td></tr><tr><td>aplayer</td><td>【可选】在需要的页面加载 aplayer 的 js 和 css,请参考文章下面的<code>音乐</code> 配置</td></tr><tr><td>highlight_shrink</td><td>【可选】配置代码框是否展开(true/false)(默认为设置中 highlight_shrink 的配置)</td></tr><tr><td>aside</td><td>【可选】显示侧边栏 (默认 true)</td></tr><tr><td>swiper_index</td><td>【可选】首页轮播图配置 index 索引，数字越小越靠前</td></tr><tr><td>top_group_index</td><td>【可选】首页右侧卡片组配置, 数字越小越靠前</td></tr><tr><td>background</td><td>【可选】文章背景可配置为 16 进制颜色值</td></tr></tbody></table><ol><li>首页轮播图配置: <code>swiper_index</code>, 数字越小越靠前</li><li>首页卡片配置: <code>top_group_index</code>, 数字越小越靠前</li><li>page 中<code>background</code>, 可配置为 16 进制颜色值</li></ol></div><!--]--></div><p>只需要在你的文章顶部的<code>Front-matter</code>配置这两个字段即可显示轮播图和推荐卡片</p><h2 id="标签页" tabindex="-1"><a class="header-anchor" href="#标签页" aria-hidden="true">#</a> 标签页</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo new page tags
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>你会找到 <code>source/tags/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;tags&quot;</code></p></li></ol><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span> 标签
<span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2021-04-06 12:01:51</span>
<span class="token key atrule">type</span><span class="token punctuation">:</span> <span class="token string">&quot;tags&quot;</span>
<span class="token key atrule">comments</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span></span>
<span class="token punctuation">---</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>解释</th></tr></thead><tbody><tr><td>type</td><td>【必须】页面类型，必须为 tags</td></tr><tr><td>comments</td><td>【可选】是否显示评论</td></tr><tr><td>top_img</td><td>【可选】是否显示顶部图</td></tr><tr><td>orderby</td><td>【可选】排序方式 ：random/name/length</td></tr><tr><td>order</td><td>【可选】排序次序： 1, asc for ascending; -1, desc for descending</td></tr></tbody></table><p><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/6432634045c13.png!blogimg" alt="标签页详情" loading="lazy"><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/6432637cecf77.png!blogimg" alt="标签页" loading="lazy"></p><h2 id="分类页" tabindex="-1"><a class="header-anchor" href="#分类页" aria-hidden="true">#</a> 分类页</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo new page categories
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>你会找到 <code>source/categories/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;categories&quot;</code></p></li></ol><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span> 分类
<span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2022-02-23 17:56:00</span>
<span class="token key atrule">aside</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">type</span><span class="token punctuation">:</span> <span class="token string">&quot;categories&quot;</span></span>
<span class="token punctuation">---</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><div class="hint-container info"><p class="hint-container-title">相关信息</p><p>分类页面存在自定义界面，本站使用自定义页面显示。</p><details class="hint-container details"><summary>自定义分类界面</summary><ol><li>首先去除<code>source/categories/index.md</code>文件中的<code>type: &quot;categories&quot;</code></li><li>修改<code>source/categories/index.md</code>文件，具体内容可自行修改链接与文字。</li></ol><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span> 分类
<span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2022-02-23 17:56:00</span>
<span class="token key atrule">aside</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span></span>
<span class="token punctuation">---</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>style</span><span class="token punctuation">&gt;</span></span>
  <span class="token title important"><span class="token punctuation">#</span>libCategories .card-wrap:hover .card-info:after {</span>
    width: 300%;
  }
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>style</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>link</span> <span class="token attr-name">rel</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>stylesheet<span class="token punctuation">&quot;</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>text/css<span class="token punctuation">&quot;</span></span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>https://npm.elemecdn.com/js-heo@1.0.11/3dCard/no3d.css<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>libCategories<span class="token punctuation">&#39;</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>lib-cards<span class="token punctuation">&quot;</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>container<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>javascript:void(0);<span class="token punctuation">&#39;</span></span> <span class="token attr-name">onClick</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>pjax.loadUrl(&quot;/categories/前端开发/&quot;)<span class="token punctuation">&#39;</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>card</span> <span class="token attr-name">data-image</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>https://img02.anheyu.com/adminuploads/1/2022/09/05/6315e144528fb.webp<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">slot</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>header<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>前端<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">slot</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>content<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>前端学习之路。<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>card</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>javascript:void(0);<span class="token punctuation">&#39;</span></span> <span class="token attr-name">onClick</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>pjax.loadUrl(&quot;/categories/大学生涯/&quot;)<span class="token punctuation">&#39;</span></span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>card</span> <span class="token attr-name">data-image</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>https://img02.anheyu.com/adminuploads/1/2022/09/05/6315e1433f197.webp<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">slot</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>header<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>大学生活<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">slot</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>content<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>在大学期间发生的一些事儿。<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>card</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>javascript:void(0);<span class="token punctuation">&#39;</span></span> <span class="token attr-name">onClick</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>pjax.loadUrl(&quot;/categories/生活日常/&quot;)<span class="token punctuation">&#39;</span></span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>card</span> <span class="token attr-name">data-image</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>https://img02.anheyu.com/adminuploads/1/2022/09/05/6315e142a69a9.webp<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h1</span> <span class="token attr-name">slot</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>header<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>生活<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h1</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>p</span> <span class="token attr-name">slot</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>content<span class="token punctuation">&quot;</span></span><span class="token punctuation">&gt;</span></span>和好朋友👬们一起经历的有趣事。<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>p</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>card</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&#39;</span>https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js<span class="token punctuation">&#39;</span></span> <span class="token attr-name">data-pjax</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>text/javascript<span class="token punctuation">&quot;</span></span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation attr-equals">=</span><span class="token punctuation">&quot;</span>https://npm.elemecdn.com/anzhiyu-theme-static@1.0.7/no3d/no3d.js<span class="token punctuation">&quot;</span></span> <span class="token attr-name">data-pjax</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></details></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/643263a321142.png!blogimg" alt="分类页" tabindex="0" loading="lazy"><figcaption>分类页</figcaption></figure><h2 id="首页即刻说说页面配置" tabindex="-1"><a class="header-anchor" href="#首页即刻说说页面配置" aria-hidden="true">#</a> 首页即刻说说页面配置</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p></li></ol><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>  hexo new page essay
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><ol start="3"><li><p>你会找到 <code>source/essay/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;essay&quot;</code></p></li></ol><div class="language-MARKDOWN line-numbers-mode" data-ext="MARKDOWN"><pre class="language-MARKDOWN"><code>  ---
  title: 即刻短文
  date: 2020-07-22 22:06:17
  comments: true
  aside: false
  top_img: false
  type: essay
  ---
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><ol start="5"><li>添加数据，新建文件<code>[blog]\source\_data\essay.yml</code>,没有<code>_data</code>文件夹的话也请自己新建。以下是默认格式示例，打开<code>[blog]\source\_data\essay.yml</code>，输入：</li></ol><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token punctuation">-</span> <span class="token key atrule">title</span><span class="token punctuation">:</span> 即刻短文
  <span class="token key atrule">subTitle</span><span class="token punctuation">:</span> 咸鱼的日常生活。
  <span class="token key atrule">tips</span><span class="token punctuation">:</span> 随时随地，分享生活
  <span class="token key atrule">buttonText</span><span class="token punctuation">:</span> 关于我
  <span class="token key atrule">buttonLink</span><span class="token punctuation">:</span> /about/
  <span class="token key atrule">limit</span><span class="token punctuation">:</span> <span class="token number">30</span>
  <span class="token key atrule">home_essay</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">top_background</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/06/15/648af1d5e6f39.jpeg
  <span class="token key atrule">essay_list</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 音乐支持了参数设置自定义歌单
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2023/01/02
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//blog.anheyu.com/music/<span class="token punctuation">?</span>id=7269231710<span class="token important">&amp;server=tencent</span>
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 关于页的打赏仿了b站的充电功能，使用svg绘图➕一些动画参数移动，应该不会被b站警告吧😜，另外文章也支持了顶部随机b站同款春秋冬banner。
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/12/18
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> React中不能直接修改state的一个重要原因是在性能优化时的prueComponment会进行浅层比较会认为是用一个对象且不能进入队列中批量更新
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/12/10
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 好耶，马上就可以放假回家了！好想家里的好吃的😋！才不是想捏妹妹的脸了
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/12/06
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 全局音乐的动画也处理好了<span class="token punctuation">,</span> nice<span class="token tag">!</span>
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/11/13
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 把页脚<span class="token punctuation">,</span> 首页顶部全都魔改到本地了<span class="token punctuation">,</span> 方便后续魔改<span class="token punctuation">,</span> 音乐也改成胶囊的样式了<span class="token punctuation">,</span> 其实还是想让胶囊可拖拽<span class="token punctuation">,</span> 不可点击改变歌词位置的<span class="token punctuation">,</span> 但是弄了半天都没弄好就放弃了
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/11/13
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 朋友圈船新版本终于写完了<span class="token punctuation">,</span> 耶✌️
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/11/05
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//blog.anheyu.com/album/
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 终于把相册集搞定了<span class="token punctuation">,</span> 耶✌️<span class="token punctuation">,</span> 瀑布流在滑动滚动条一个视口范围上下100的情况执行一次<span class="token punctuation">,</span> 到底部停止监听让性能高了好多，再也不会布局混乱🤪了
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/10/25
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//blog.anheyu.com/album/
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 搜索🔍支持缩略图显示啦（默认获取文章内容的第一张图片）
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/10/23 08<span class="token punctuation">:</span><span class="token datetime number">00:00</span>
      <span class="token key atrule">from</span><span class="token punctuation">:</span> 安知鱼
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 遇见彩虹🌈吃定彩虹
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/10/23 10<span class="token punctuation">:</span><span class="token datetime number">00:00</span>
      <span class="token key atrule">image</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/64329399e285d.webp
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/64329399aa3bc.webp
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/6432939996dd7.webp
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> ThreeJs API真多丫
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/10/19
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 妹妹强制要求我买走了她的两幅画 <span class="token punctuation">-</span>¥30
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/10/02
      <span class="token key atrule">image</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/643293997b92b.jpeg
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 歌曲推荐
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/09/25
      <span class="token key atrule">aplayer</span><span class="token punctuation">:</span>
        <span class="token key atrule">server</span><span class="token punctuation">:</span> tencent
        <span class="token key atrule">id</span><span class="token punctuation">:</span> 001FGQba3i10mw
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> 做了一个噩梦<span class="token punctuation">,</span> 梦到从楼顶坠下去了。😷
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/09/24
    <span class="token punctuation">-</span> <span class="token key atrule">content</span><span class="token punctuation">:</span> JOJO是真的好看！
      <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/09/21
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.bilibili.com/bangumi/play/ss39431<span class="token punctuation">?</span>spm_id_from=333.337.0.0
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>主题配置文件中开启<code>menu</code>中关于和闲言碎语的注释，导航栏闲言碎语，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token comment"># 友链:</span>
  <span class="token comment">#   友人帐: /link/ || anzhiyu-icon-link</span>
  <span class="token comment">#   朋友圈: /fcircle/ || anzhiyu-icon-artstation</span>
  <span class="token comment">#   留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token comment"># 我的:</span>
  <span class="token comment">#   音乐馆: /music/ || anzhiyu-icon-music</span>
  <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
  <span class="token comment">#   相册集: /album/ || anzhiyu-icon-images</span>
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token comment">#   关于本人: /about/ || anzhiyu-icon-paper-plane</span>
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><div class="hint-container warning"><p class="hint-container-title">警告</p><p>示例数据中的图片不保证可用性，请自行更换为您自己的图床链接。图床相关知识=&gt;<a href="https://blog.anheyu.com/posts/2785.html" target="_blank" rel="noopener noreferrer">我的图床方案<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/643263bdd2aa4.png!blogimg" alt="即刻说说页面" tabindex="0" loading="lazy"><figcaption>即刻说说页面</figcaption></figure><h2 id="友情链接配置" tabindex="-1"><a class="header-anchor" href="#友情链接配置" aria-hidden="true">#</a> 友情链接配置</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo new page <span class="token function">link</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>你会找到 <code>source/link/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;link&quot;</code></p><div class="language-MARKDOWN line-numbers-mode" data-ext="MARKDOWN"><pre class="language-MARKDOWN"><code>  ---
  title: link
  date: 2020-12-01 22:19:45
  type: &#39;link&#39;
  ---
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li></ol><p>新建文件<code>[blog]\source\_data\link.yml</code>,没有<code>_data</code>文件夹的话也请自己新建。以下是默认友链格式示例(<s>自己写的教程，夹带点私货不过分吧，嘻嘻</s>)。打开<code>[blog]\source\_data\link.yml</code>，输入：</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> 框架
  <span class="token key atrule">flink_style</span><span class="token punctuation">:</span> flexcard
  <span class="token key atrule">link_list</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> Hexo
      <span class="token key atrule">hundredSuffix</span><span class="token punctuation">:</span> <span class="token string">&quot;&quot;</span>
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//hexo.io/zh<span class="token punctuation">-</span>tw/
      <span class="token key atrule">avatar</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//d33wubrfki0l68.cloudfront.net/6657ba50e702d84afb32fe846bed54fba1a77add/827ae/logo.svg
      <span class="token key atrule">descr</span><span class="token punctuation">:</span> 快速、简单且强大的网站框架
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> anzhiyu主题
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//blog.anheyu.com/
      <span class="token key atrule">avatar</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//img02.anheyu.com/adminuploads/1/2022/09/15/63232b7d91d22.jpg
      <span class="token key atrule">descr</span><span class="token punctuation">:</span> 生活明朗，万物可爱
      <span class="token key atrule">siteshot</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog@1.1.6/img/post/common/anzhiy.cn.jpg

<span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> 推荐博客
  <span class="token key atrule">flink_style</span><span class="token punctuation">:</span> flexcard
  <span class="token key atrule">link_list</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 安知鱼
      <span class="token key atrule">hundredSuffix</span><span class="token punctuation">:</span> <span class="token string">&quot;&quot;</span>
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//blog.anheyu.com/
      <span class="token key atrule">avatar</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//img02.anheyu.com/adminuploads/1/2022/09/15/63232b7d91d22.jpg
      <span class="token key atrule">descr</span><span class="token punctuation">:</span> 生活明朗，万物可爱
      <span class="token key atrule">siteshot</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog@1.1.6/img/post/common/anzhiy.cn.jpg

<span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> 小伙伴
  <span class="token key atrule">class_desc</span><span class="token punctuation">:</span> 那些人，那些事
  <span class="token key atrule">flink_style</span><span class="token punctuation">:</span> anzhiyu
  <span class="token key atrule">link_list</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 安知鱼
      <span class="token key atrule">hundredSuffix</span><span class="token punctuation">:</span> <span class="token string">&quot;&quot;</span>
      <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//blog.anheyu.com
      <span class="token key atrule">avatar</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//img02.anheyu.com/adminuploads/1/2022/09/15/63232b7d91d22.jpg
      <span class="token key atrule">descr</span><span class="token punctuation">:</span> 生活明朗，万物可爱
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>会根据 flink_style 产生两种效果，可选值有<code>flexcard</code>或者<code>anzhiyu</code></p><p>当友链数目超过 50 以后会触发，与博主共同进步板块，可以自行配置 <code>hundredSuffix: &quot;!w120&quot;</code>,该参数可以解决共同进步板块头像质量问题，配置后共同进步板块的头像会添加该后缀。</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">flink_style</span><span class="token punctuation">:</span> flexcard <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>主题配置文件中开启<code>menu</code>中友链和友人帐的注释，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token key atrule">友链</span><span class="token punctuation">:</span>
    <span class="token key atrule">友人帐</span><span class="token punctuation">:</span> /link/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>link
  <span class="token comment">#   朋友圈: /fcircle/ || anzhiyu-icon-artstation</span>
  <span class="token comment">#   留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token comment"># 我的:</span>
  <span class="token comment">#   音乐馆: /music/ || anzhiyu-icon-music</span>
  <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
  <span class="token comment">#   相册集: /album/ || anzhiyu-icon-images</span>
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token comment">#   关于本人: /about/ || anzhiyu-icon-paper-plane</span>
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/6432641611b97.png!blogimg" alt="友情链接页" tabindex="0" loading="lazy"><figcaption>友情链接页</figcaption></figure><h2 id="关于页面配置" tabindex="-1"><a class="header-anchor" href="#关于页面配置" aria-hidden="true">#</a> 关于页面配置</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo new page about
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>你会找到 <code>source/about/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;about&quot;</code></p><div class="language-MARKDOWN line-numbers-mode" data-ext="MARKDOWN"><pre class="language-MARKDOWN"><code>  ---
  title: 关于
  date: 2021-03-30 15:57:51
  aside: false
  top_img: false
  background: &quot;#f8f9fe&quot;
  comments: false
  type: &quot;about&quot;
  ---
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li><li><p>主题配置文件中开启<code>menu</code>中关于和关于本人的注释，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token key atrule">友链</span><span class="token punctuation">:</span>
    <span class="token key atrule">友人帐</span><span class="token punctuation">:</span> /link/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>link
  <span class="token comment">#   朋友圈: /fcircle/ || anzhiyu-icon-artstation</span>
  <span class="token comment">#   留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token key atrule">我的</span><span class="token punctuation">:</span>
    <span class="token key atrule">音乐馆</span><span class="token punctuation">:</span> /music/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>music
  <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
  <span class="token comment">#   相册集: /album/ || anzhiyu-icon-images</span>
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token key atrule">关于本人</span><span class="token punctuation">:</span> /about/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>paper<span class="token punctuation">-</span>plane
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li></ol><p>新建<code>source/_data/about.yml</code>，输入以下内容</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> 关于页
  <span class="token key atrule">subtitle</span><span class="token punctuation">:</span> 生活明朗，万物可爱✨
  <span class="token key atrule">avatarImg</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog<span class="token punctuation">-</span>static@1.0.0/img/avatar.webp
  <span class="token key atrule">name</span><span class="token punctuation">:</span> 陈志伟
  <span class="token key atrule">description</span><span class="token punctuation">:</span> 是一名 前端工程师、学生、独立开发者、博主
  <span class="token key atrule">aboutsiteTips</span><span class="token punctuation">:</span>
    <span class="token key atrule">tips</span><span class="token punctuation">:</span> 追求
    <span class="token key atrule">title1</span><span class="token punctuation">:</span> 源于
    <span class="token key atrule">title2</span><span class="token punctuation">:</span> 热爱而去 感受
    <span class="token key atrule">word</span><span class="token punctuation">:</span>
      <span class="token punctuation">-</span> 学习
      <span class="token punctuation">-</span> 生活
      <span class="token punctuation">-</span> 程序
      <span class="token punctuation">-</span> 体验
  <span class="token key atrule">helloAbout</span><span class="token punctuation">:</span> Hello there<span class="token tag">!</span>
  <span class="token key atrule">skillsTips</span><span class="token punctuation">:</span>
    <span class="token key atrule">tips</span><span class="token punctuation">:</span> 技能
    <span class="token key atrule">title</span><span class="token punctuation">:</span> 开启创造力
  <span class="token key atrule">careers</span><span class="token punctuation">:</span>
    <span class="token key atrule">tips</span><span class="token punctuation">:</span> 生涯
    <span class="token key atrule">title</span><span class="token punctuation">:</span> 无限进步
    <span class="token key atrule">item</span><span class="token punctuation">:</span>
      <span class="token punctuation">-</span> EDU<span class="token punctuation">,</span>软件工程专业
    <span class="token key atrule">img</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/21/644287166329b.png
  <span class="token key atrule">statistic</span><span class="token punctuation">:</span>
    <span class="token key atrule">link</span><span class="token punctuation">:</span> /archives
    <span class="token key atrule">text</span><span class="token punctuation">:</span> 文章隧道
    <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/05/01/644f4b037b930.jpg
  <span class="token key atrule">map</span><span class="token punctuation">:</span>
    <span class="token key atrule">title</span><span class="token punctuation">:</span> 我现在住在
    <span class="token key atrule">StrengthenTitle</span><span class="token punctuation">:</span> 中国，长沙市
    <span class="token key atrule">background</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c61cb20ef.jpg
    <span class="token key atrule">backgroundDark</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c63495ac5.jpg
  <span class="token key atrule">selfInfo</span><span class="token punctuation">:</span>
    <span class="token key atrule">selfInfoTips1</span><span class="token punctuation">:</span> 生于
    <span class="token key atrule">selfInfoContentYear</span><span class="token punctuation">:</span> <span class="token number">2002</span>
    <span class="token key atrule">selfInfoTips2</span><span class="token punctuation">:</span> 湖南信息学院
    <span class="token key atrule">selfInfoContent2</span><span class="token punctuation">:</span> 软件工程
    <span class="token key atrule">selfInfoTips3</span><span class="token punctuation">:</span> 现在职业
    <span class="token key atrule">selfInfoContent3</span><span class="token punctuation">:</span> 大三学生👨‍🎓
  <span class="token key atrule">personalities</span><span class="token punctuation">:</span>
    <span class="token key atrule">author_name</span><span class="token punctuation">:</span> 执政官
    <span class="token key atrule">personality_type</span><span class="token punctuation">:</span> ESFJ<span class="token punctuation">-</span>A
    <span class="token key atrule">photo_url</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c63495ac5.jpg
    <span class="token key atrule">personality_img</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog@2.0.8/img/svg/ESFJ<span class="token punctuation">-</span>A.svg
    <span class="token key atrule">name_url</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.16personalities.com/ch/esfj<span class="token punctuation">-</span>%E4%BA%BA%E6%A0%BC
  <span class="token key atrule">maxim</span><span class="token punctuation">:</span>
    <span class="token key atrule">maxim_tips</span><span class="token punctuation">:</span> 座右铭
    <span class="token key atrule">maxim_top</span><span class="token punctuation">:</span> 生活明朗，
    <span class="token key atrule">maxim_bottom</span><span class="token punctuation">:</span> 万物可爱。
  <span class="token key atrule">buff</span><span class="token punctuation">:</span>
    <span class="token key atrule">buff_tips</span><span class="token punctuation">:</span> 特长
    <span class="token key atrule">buff_top</span><span class="token punctuation">:</span> 脑回路新奇的 酸菜鱼
    <span class="token key atrule">buff_bottom</span><span class="token punctuation">:</span> 二次元指数 MAX
  <span class="token key atrule">game</span><span class="token punctuation">:</span>
    <span class="token key atrule">game_tips</span><span class="token punctuation">:</span> 爱好游戏
    <span class="token key atrule">game_title</span><span class="token punctuation">:</span> 原神
    <span class="token key atrule">game_uid</span><span class="token punctuation">:</span> <span class="token string">&quot;UID: 125766904&quot;</span>
    <span class="token key atrule">game_bg</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/22/64433bf26e25d.webp
  <span class="token key atrule">comic</span><span class="token punctuation">:</span>
    <span class="token key atrule">comic_tips</span><span class="token punctuation">:</span> 爱好番剧
    <span class="token key atrule">comic_title</span><span class="token punctuation">:</span> 追番
    <span class="token key atrule">comic_list</span><span class="token punctuation">:</span>
      <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 约定的梦幻岛
        <span class="token key atrule">href</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.bilibili.com/bangumi/media/md5267750/<span class="token punctuation">?</span>spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
        <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/05/27/647166c44b414.webp
      <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 咒术回战
        <span class="token key atrule">href</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.bilibili.com/bangumi/media/md28229899/<span class="token punctuation">?</span>spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
        <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/05/24/646db4398832e.webp
      <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 紫罗兰永恒花园
        <span class="token key atrule">href</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.bilibili.com/bangumi/media/md8892/<span class="token punctuation">?</span>spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
        <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/05/24/646db43983d99.webp
      <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 鬼灭之刃
        <span class="token key atrule">href</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.bilibili.com/bangumi/media/md22718131/<span class="token punctuation">?</span>spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
        <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/05/24/646db439856a0.webp
      <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> JOJO的奇妙冒险 黄金之风
        <span class="token key atrule">href</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.bilibili.com/bangumi/media/md135652/<span class="token punctuation">?</span>spm_id_from=666.25.b_6d656469615f6d6f64756c65.1
        <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/05/30/64760e38d651a.webp
  <span class="token key atrule">like</span><span class="token punctuation">:</span>
    <span class="token key atrule">like_tips</span><span class="token punctuation">:</span> 关注偏好
    <span class="token key atrule">like_title</span><span class="token punctuation">:</span> 数码科技
    <span class="token key atrule">like_bg</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2022/12/06/638f5f05ce1f7.jpg
    <span class="token key atrule">like_bottom</span><span class="token punctuation">:</span> 手机、电脑软硬件
  <span class="token key atrule">music</span><span class="token punctuation">:</span>
    <span class="token key atrule">music_tips</span><span class="token punctuation">:</span> 音乐偏好
    <span class="token key atrule">music_title</span><span class="token punctuation">:</span> 许嵩、民谣、华语流行
    <span class="token key atrule">music_bg</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//p2.music.126.net/Mrg1i7DwcwjWBvQPIMt_Mg==/79164837213438.jpg
    <span class="token key atrule">music_link</span><span class="token punctuation">:</span> /music
  <span class="token key atrule">reward_list</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 海阔蓝
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">8.8</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2023-03-28</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> LK66
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">66.6</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2023-03-24</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 张时貳
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">6.6</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2023-01-22</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> ZeroAf
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">9.9</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-12-14</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> LuckyWangXi
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">6.6</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-12-14</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 刀中日月长
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">10</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-11-16</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 鹿啵包
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">10</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-11-08</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 疾速k
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">50</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-09-20</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 伴舟先生大霖子
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">4.03</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-10-27</span>
      <span class="token key atrule">suffix</span><span class="token punctuation">:</span> 贝壳
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> Magica_0x0
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">3.36</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-10-07</span>
      <span class="token key atrule">suffix</span><span class="token punctuation">:</span> 贝壳
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 名字就是要短像这样
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">3.36</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-08-25</span>
      <span class="token key atrule">suffix</span><span class="token punctuation">:</span> 贝壳
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> Leviathan520
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">1.34</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-08-23</span>
      <span class="token key atrule">suffix</span><span class="token punctuation">:</span> 贝壳
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 托马斯
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">10</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-08-19</span>
    <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 哇是猫猫欸
      <span class="token key atrule">amount</span><span class="token punctuation">:</span> <span class="token number">1.34</span>
      <span class="token key atrule">datatime</span><span class="token punctuation">:</span> <span class="token datetime number">2022-08-19</span>
      <span class="token key atrule">suffix</span><span class="token punctuation">:</span> 贝壳

  <span class="token key atrule">extra</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>备选值/类型</th><th>解释</th></tr></thead><tbody><tr><td>class_name</td><td>关于页</td><td>【必须】页面类</td></tr><tr><td>subtitle</td><td>string</td><td>【必须】副标题</td></tr><tr><td>avatarImg</td><td>url</td><td>【必须】头像链接</td></tr><tr><td>name</td><td>string</td><td>【必须 作者名称</td></tr><tr><td>description</td><td>string</td><td>【必须】描述</td></tr><tr><td>aboutsiteTips</td><td>object</td><td>【必须】站点关于提示相关配置</td></tr><tr><td>aboutsiteTips.tips</td><td>string</td><td>【必须】站点关于提示性文字</td></tr><tr><td>aboutsiteTips.title1</td><td>string</td><td>【必须】站点关于标题文字 1</td></tr><tr><td>aboutsiteTips.title2</td><td>string</td><td>【必须】站点关于标题文字 2</td></tr><tr><td>aboutsiteTips.word</td><td>list</td><td>【必须】站点关于标题滚动文字</td></tr><tr><td>helloAbout</td><td>string</td><td>【必须】hello 文字</td></tr><tr><td>skillsTips</td><td>object</td><td>【必须】技能相关配置</td></tr><tr><td>skillsTips.tips</td><td>string</td><td>【必须】技能提示文字</td></tr><tr><td>skillsTips.title</td><td>string</td><td>【必须】技能标题</td></tr><tr><td>careers</td><td>object</td><td>【必须】生涯相关配置</td></tr><tr><td>careers.tips</td><td>string</td><td>【必须】生涯提示性文字</td></tr><tr><td>careers.title</td><td>string</td><td>【必须】生涯标题</td></tr><tr><td>careers.item</td><td>list</td><td>【必须】生涯 item</td></tr><tr><td>careers.img</td><td>string</td><td>【必须】生涯图片</td></tr><tr><td>statistic</td><td>object</td><td>【必须】统计数据相关配置</td></tr><tr><td>statistic.link</td><td>url</td><td>【必须】统计数据按钮前往链接</td></tr><tr><td>statistic.text</td><td>string</td><td>【必须】统计数据按钮文字</td></tr><tr><td>map</td><td>object</td><td>【必须】地图相关配置</td></tr><tr><td>map.title</td><td>string</td><td>【必须】地图标题</td></tr><tr><td>map.StrengthenTitle</td><td>string</td><td>【必须】地图大标题</td></tr><tr><td>map.background</td><td>url</td><td>【必须】地图亮色模式背景</td></tr><tr><td>map.backgroundDark</td><td>url</td><td>【必须】地图暗色模式背景</td></tr><tr><td>selfInfo</td><td>object</td><td>【必须】作者相关信息配置</td></tr><tr><td>selfInfo.selfInfoTips1</td><td>string</td><td>【必须】作者相关提示文字 1</td></tr><tr><td>selfInfo.selfInfoContentYear</td><td>number</td><td>【必须】作者生日年份</td></tr><tr><td>selfInfo.selfInfoTips2</td><td>string</td><td>【必须】作者相关提示文字 2</td></tr><tr><td>selfInfo.selfInfoContent2</td><td>string</td><td>【必须】作者相关内容 2</td></tr><tr><td>selfInfo.selfInfoTips3</td><td>string</td><td>【必须】作者相关提示文字 3</td></tr><tr><td>selfInfo.selfInfoContent3</td><td>string</td><td>【必须】作者相关内容 3</td></tr><tr><td>personalities</td><td>object</td><td>【必须】作者性格相关配置</td></tr><tr><td>personalities.author_name</td><td>string</td><td>【必须】作者性格名称</td></tr><tr><td>personalities.personality_type</td><td>string</td><td>【必须】作者性格类型</td></tr><tr><td>personalities.photo_url</td><td>url</td><td>【必须】作者自拍图片</td></tr><tr><td>personalities.personality_img</td><td>url</td><td>【必须】作者性格表述图片</td></tr><tr><td>personalities.name_url</td><td>url</td><td>【必须】点击性格跳转到链接</td></tr><tr><td>maxim</td><td>object</td><td>【必须】座右铭相关配置</td></tr><tr><td>maxim.maxim_tips</td><td>string</td><td>【必须】座右铭相关提示文字</td></tr><tr><td>maxim.maxim_top</td><td>string</td><td>【必须】座右铭相关顶部文字</td></tr><tr><td>maxim.maxim_bottom</td><td>string</td><td>【必须】座右铭相关底部文字</td></tr><tr><td>buff</td><td>object</td><td>【必须】特长相关配置</td></tr><tr><td>buff.buff_tips</td><td>string</td><td>【必须】特长相关提示文字</td></tr><tr><td>buff.buff_top</td><td>string</td><td>【必须】特长相关顶部文字</td></tr><tr><td>buff.buff_bottom</td><td>string</td><td>【必须】特长相关底部文字</td></tr><tr><td>game</td><td>object</td><td>【必须】爱好游戏相关配置</td></tr><tr><td>game.game_tips</td><td>string</td><td>【必须】爱好游戏提示文字</td></tr><tr><td>game.game_title</td><td>string</td><td>【必须】爱好游戏标题</td></tr><tr><td>game.game_uid</td><td>string</td><td>【必须】爱好游戏 uid</td></tr><tr><td>game.game_bg</td><td>url</td><td>【必须】爱好游戏背景</td></tr><tr><td>comic</td><td>object</td><td>【必须】追番相关配置，需要 5 条数据</td></tr><tr><td>comic.comic_tips</td><td>string</td><td>【必须】追番相关提示文字</td></tr><tr><td>comic.comic_title</td><td>string</td><td>【必须】追番相关标题</td></tr><tr><td>comic.comic_list</td><td>list</td><td>【必须】追番相关列表</td></tr><tr><td>comic.comic_list.name</td><td>string</td><td>【必须】追番 item 名称</td></tr><tr><td>comic.comic_list.href</td><td>url</td><td>【必须】追番 item 链接</td></tr><tr><td>comic.comic_list.cover</td><td>url</td><td>【必须】追番 item 的 cover</td></tr><tr><td>like</td><td>object</td><td>【必须】关注偏好相关配置</td></tr><tr><td>like.like_tips</td><td>string</td><td>【必须】关注偏好配置提示文字</td></tr><tr><td>like.like_title</td><td>string</td><td>【必须】关注偏好配置标题</td></tr><tr><td>like.like_bg</td><td>url</td><td>【必须】关注偏好配置背景</td></tr><tr><td>like.like_bottom</td><td>string</td><td>【必须】关注偏好配置底部文字</td></tr><tr><td>music</td><td>object</td><td>【必须】音乐偏好相关配置</td></tr><tr><td>music.music_tips</td><td>string</td><td>【必须】音乐偏好提示性文字</td></tr><tr><td>music.music_title</td><td>string</td><td>【必须】音乐偏好标题</td></tr><tr><td>music.music_bg</td><td>url</td><td>【必须】音乐偏好背景</td></tr><tr><td>music.music_link</td><td>url</td><td>【必须】音乐偏好按钮链接</td></tr><tr><td>reward_list</td><td>object</td><td>【可选】打赏相关配置，如果不配置将没有打赏模块</td></tr><tr><td>reward_list.name</td><td>string</td><td>【必须】打赏 item 名称</td></tr><tr><td>reward_list.amount</td><td>number</td><td>【必须】打赏 item 金额</td></tr><tr><td>reward_list.datatime</td><td>Date</td><td>【必须】打赏 item 时间</td></tr><tr><td>reward_list.suffix</td><td>string/元</td><td>【可选】打赏 item 后缀（默认元）</td></tr><tr><td>extra</td><td>boolean/false</td><td>【必须】开发字段，表示额外模块内容可通过修改主题<code>themes/anzhiyu/layout/includes/anzhiyu/about-extra.pug</code>自行开发，修改为 true 后会引入该 pug 内容。</td></tr></tbody></table><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/6432643720ef6.png!blogimg" alt="关于页" tabindex="0" loading="lazy"><figcaption>关于页</figcaption></figure><h2 id="配置相册页面" tabindex="-1"><a class="header-anchor" href="#配置相册页面" aria-hidden="true">#</a> 配置相册页面</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo new page album
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>你会找到 <code>source/album/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;album&quot;</code></p><div class="language-MARKDOWN line-numbers-mode" data-ext="MARKDOWN"><pre class="language-MARKDOWN"><code>  ---
  title: 相册集
  date: 2022-10-23 15:57:51
  aside: false
  top_img: false
  type: &quot;album&quot;
  top_background: https://bu.dusays.com/2023/06/30/649e54029be36.webp
  ---
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>需要修改顶部图可以修改<code>top_background</code>字段的链接，主题版本需<code>1.4.1</code>或以上。</p></li><li><p>主题配置文件中开启<code>menu</code>中我的和相册集的注释，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token key atrule">友链</span><span class="token punctuation">:</span>
    <span class="token key atrule">友人帐</span><span class="token punctuation">:</span> /link/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>link
  <span class="token comment">#   朋友圈: /fcircle/ || anzhiyu-icon-artstation</span>
  <span class="token comment">#   留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token key atrule">我的</span><span class="token punctuation">:</span>
    <span class="token key atrule">音乐馆</span><span class="token punctuation">:</span> /music/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>music
    <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
    <span class="token key atrule">相册集</span><span class="token punctuation">:</span> /album/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>images
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token comment">#   关于本人: /about/ || anzhiyu-icon-paper-plane</span>
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li></ol><p>新建文件<code>[blog]\source\_data\album.yml</code>,没有<code>_data</code>文件夹的话也请自己新建。打开<code>[blog]\source\_data\album.yml</code>，输入：</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> 世界各地夕阳与风景
  <span class="token key atrule">path_name</span><span class="token punctuation">:</span> /wordScenery
  <span class="token key atrule">type</span><span class="token punctuation">:</span> <span class="token number">2</span>
  <span class="token key atrule">description</span><span class="token punctuation">:</span> 因为到不了世界各地，所以请网友们发来了各地的夕阳与风景🌇。
  <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/64329399d1175.jpg
  <span class="token key atrule">top_background</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/06/30/649e546ada7dd.webp
  <span class="token key atrule">rowHeight</span><span class="token punctuation">:</span> <span class="token number">220</span>
  <span class="token key atrule">limit</span><span class="token punctuation">:</span> <span class="token number">10</span>
  <span class="token key atrule">lazyload</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">btnLazyload</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">url</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">album_list</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">date</span><span class="token punctuation">:</span> 2022/10/26 01<span class="token punctuation">:</span><span class="token datetime number">00:00</span>
      <span class="token key atrule">content</span><span class="token punctuation">:</span> 湘潭的一角。
      <span class="token key atrule">address</span><span class="token punctuation">:</span> 湖南湘潭
      <span class="token key atrule">from</span><span class="token punctuation">:</span> 再吃一口就减肥
      <span class="token key atrule">image</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/64329399db122.webp
    <span class="token punctuation">-</span> <span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2022-10-25</span>
      <span class="token key atrule">content</span><span class="token punctuation">:</span> 洛阳暴雨后的天空。
      <span class="token key atrule">address</span><span class="token punctuation">:</span> 河南洛阳
      <span class="token key atrule">from</span><span class="token punctuation">:</span> 紫菜卷
      <span class="token key atrule">image</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/64329399db122.webp
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/64329399db2e1.webp

<span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> 我的日常
  <span class="token key atrule">path_name</span><span class="token punctuation">:</span> /dailyPhoto
  <span class="token key atrule">type</span><span class="token punctuation">:</span> <span class="token number">1</span>
  <span class="token key atrule">description</span><span class="token punctuation">:</span> 这里存放的是有关我自己的一些沙雕生活与有趣的事情。
  <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/64329399cea5a.webp
  <span class="token key atrule">album_list</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2022-10-24</span>
      <span class="token key atrule">content</span><span class="token punctuation">:</span> 老妹的画
      <span class="token key atrule">image</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/04/09/643293997b92b.jpeg
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>备选值/类型</th><th>解释</th></tr></thead><tbody><tr><td>class_name</td><td>string</td><td>【必须】页面类</td></tr><tr><td>path_name</td><td>url</td><td>【必须】当前相册路径</td></tr><tr><td>type</td><td>number</td><td>【必须】当前相册页面样式类型</td></tr><tr><td>description</td><td>string</td><td>【必须】当前相册描述</td></tr><tr><td>cover</td><td>url</td><td>【必须】当前相册 cover 图片</td></tr><tr><td>top_background</td><td>url</td><td>【可选】当前相册顶部 banner 图片，可以不填，主题版本需要1.4.1或以上</td></tr><tr><td>rowHeight</td><td>number</td><td>【可选】仅当 type 为 2 时有效，当前相册 rowHeight</td></tr><tr><td>limit</td><td>number</td><td>【可选】仅当 type 为 2 时有效，当前相册 一次懒加载的数量</td></tr><tr><td>lazyload</td><td>boolean</td><td>【可选】仅当 type 为 2 时有效，当前相册 lazyload 是否开启懒加载，默认懒加载为滚动懒加载，type 为 1 时懒加载不可关闭。</td></tr><tr><td>btnLazyload</td><td>boolean</td><td>【可选】仅当 type 为 2 且 lazyload 开启 时有效，当前相册 lazyload 懒加载的方式，默认为滚动懒加载，开启本选项后为按钮点击懒加载。</td></tr><tr><td>album_list</td><td>list</td><td>【必须】当前相册内图片列表</td></tr><tr><td>url</td><td>url</td><td>【可选】仅当 type 为 2 时有效，可以加载远程的 json 数据。</td></tr><tr><td>album_list.date</td><td>date</td><td>【必须】当前图片创建时间</td></tr><tr><td>album_list.content</td><td>string</td><td>【必须】当前图片描述内容</td></tr><tr><td>album_list.image</td><td>list</td><td>【必须】当前图片集，可以多张</td></tr><tr><td>album_list.from</td><td>string</td><td>【可选】当前图片的创建人，未填写则不显示</td></tr><tr><td>album_list.address</td><td>string</td><td>【必须】当前图片地址</td></tr></tbody></table><div class="hint-container warning"><p class="hint-container-title">警告</p><p>注意示例数据中的图片不保证可用性。</p></div><p>由于相册页面需要很多的 page，所以在写数据的时候自行写入路径<code>path_name</code>，示例数据中有两个<code>path_name</code>，所以需要再创建两个页面</p><p>注意新建的页面必须与<code>path_name</code>一致。</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>  hexo new page dailyPhoto
  hexo new page wordScenery
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div></div></div><p>你会找到 <code>source/dailyPhoto/index.md</code> 和<code>source/wordScenery/index.md</code>两个文件，这两个为相册集详情页</p><p>然后内容为以下内容, 需在详情页加上<code>type: &quot;album_detail&quot;</code></p><div class="language-MARKDOWN line-numbers-mode" data-ext="MARKDOWN"><pre class="language-MARKDOWN"><code>---
title: 日常生活
date: 2022-10-23 15:57:51
aside: false
top_img: false
type: &quot;album_detail&quot;
---
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><div class="language-MARKDOWN line-numbers-mode" data-ext="MARKDOWN"><pre class="language-MARKDOWN"><code>---
title: 世界各地风景
date: 2022-10-23 15:57:51
aside: false
top_img: false
type: &quot;album_detail&quot;
---
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><code>远程加载json示例数据</code></p><div class="language-json line-numbers-mode" data-ext="json"><pre class="language-json"><code><span class="token punctuation">[</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://cdn.jsdelivr.net/gh/jerryc127/CDN/img/IMG_0556.jpg&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;alt&quot;</span><span class="token operator">:</span> <span class="token string">&quot;IMG_0556.jpg&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;title&quot;</span><span class="token operator">:</span> <span class="token string">&quot;这是title&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://cdn.jsdelivr.net/gh/jerryc127/CDN/img/IMG_0472.jpg&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;alt&quot;</span><span class="token operator">:</span> <span class="token string">&quot;IMG_0472.jpg&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://cdn.jsdelivr.net/gh/jerryc127/CDN/img/IMG_0453.jpg&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;alt&quot;</span><span class="token operator">:</span> <span class="token string">&quot;&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://cdn.jsdelivr.net/gh/jerryc127/CDN/img/IMG_0931.jpg&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;alt&quot;</span><span class="token operator">:</span> <span class="token string">&quot;&quot;</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">]</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-1788-0" aria-selected="true">相册页</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-1788-1" aria-selected="false">相册页 type: 1 样式</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-1788-2" aria-selected="false">相册页 type: 2 样式</button></div><!--[--><div class="tab-item active" id="tab-1788-0" role="tabpanel" aria-expanded="true"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/64326458a0f01.png!blogimg" alt="相册页" tabindex="0" loading="lazy"><figcaption>相册页</figcaption></figure></div><div class="tab-item" id="tab-1788-1" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/19/643f4351c8245.webp!blogimg" alt="相册页" tabindex="0" loading="lazy"><figcaption>相册页</figcaption></figure></div><div class="tab-item" id="tab-1788-2" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/19/643f42162d2f4.webp!blogimg" alt="相册页" tabindex="0" loading="lazy"><figcaption>相册页</figcaption></figure></div><!--]--></div><h2 id="朋友圈页面配置" tabindex="-1"><a class="header-anchor" href="#朋友圈页面配置" aria-hidden="true">#</a> 朋友圈页面配置</h2><blockquote><p>友链朋友圈<a href="https://fcircle-doc.yyyzyyyz.cn/#/backenddeploy" target="_blank" rel="noopener noreferrer">后端部署文档<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 项目地址: <a href="https://github.com/Rock-Candy-Tea/hexo-circle-of-friends" target="_blank" rel="noopener noreferrer">https://github.com/Rock-Candy-Tea/hexo-circle-of-friends<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></blockquote><p>请注意朋友圈部署有一定难度，博主使用的为 server 部署，拉取后端代码后可将代码内的默认值改为自己的站点，theme 改为 common2，注意一定要使用<code>common2</code>，否则将无法拉取到自己的友链。</p><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>  hexo new page fcircle
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>打开<code>[blog]\source\fcircle\index.md</code>,添加一行<code>type: &#39;fcircle&#39;</code>:</p><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span> 朋友圈
<span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2022-11-21 17:06:17</span>
<span class="token key atrule">comments</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">aside</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">type</span><span class="token punctuation">:</span> <span class="token string">&quot;fcircle&quot;</span></span>
<span class="token punctuation">---</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>主题配置文件中开启<code>menu</code>中友链和朋友圈的注释，导航栏朋友圈，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token key atrule">友链</span><span class="token punctuation">:</span>
    <span class="token comment">#   友人帐: /link/ || anzhiyu-icon-link</span>
    <span class="token key atrule">朋友圈</span><span class="token punctuation">:</span> /fcircle/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>artstation
    <span class="token comment">#   留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token comment"># 我的:</span>
  <span class="token comment">#   音乐馆: /music/ || anzhiyu-icon-music</span>
  <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
  <span class="token comment">#   相册集: /album/ || anzhiyu-icon-images</span>
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token comment">#   关于本人: /about/ || anzhiyu-icon-paper-plane</span>
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>主题配置文件中开启<code>friends_vue.enable</code>，自行设置<a href="https://fcircle-doc.yyyzyyyz.cn/#/backenddeploy" target="_blank" rel="noopener noreferrer">朋友圈后端地址<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>和顶部模块背景，注意缩进！！！</p><div class="hint-container warning"><p class="hint-container-title">注意</p><p>注意后端爬取需使用<code>common2</code></p></div><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># 朋友圈配置</span>
<span class="token key atrule">friends_vue</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">vue_js</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog<span class="token punctuation">-</span>static@1.0.0/js/friends_vue/index.js
  <span class="token key atrule">apiurl</span><span class="token punctuation">:</span> <span class="token comment"># 朋友圈后端地址</span>
  <span class="token key atrule">top_background</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>备选值/类型</th><th>解释</th></tr></thead><tbody><tr><td>enable</td><td>boolean</td><td>【必须】是否启用</td></tr><tr><td>vue_js</td><td>url</td><td>【必须】朋友圈前端构建后的 url</td></tr><tr><td>apiurl</td><td>string</td><td>【必须】朋友圈后端 url</td></tr><tr><td>top_background</td><td>url</td><td>【可选】朋友圈顶部背景图</td></tr></tbody></table><p>以下是本站配置</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">friends_vue</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">vue_js</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog<span class="token punctuation">-</span>static@1.0.0/js/friends_vue/index.js
  <span class="token key atrule">apiurl</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//friends.anheyu.com/ <span class="token comment"># 朋友圈后端地址</span>
  <span class="token key atrule">top_background</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//img02.anheyu.com/adminuploads/1/2022/08/21/630249e2df20f.jpg
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>其中<code>vue_js</code>参数，可以将<code>https://npm.elemecdn.com/anzhiyu-blog-static@1.0.0/js/friends_vue/index.js</code>中的 <code>friends.anheyu.com</code>替换为您的后端 url 然后使用</p><p>前端项目地址：<a href="https://github.com/anzhiyu-c/hexo-circle-of-friends-front/" target="_blank" rel="noopener noreferrer">hexo-circle-of-friends-front<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>，也可以自行下载项目后，修改代码中的 url 变量路径<code>friends.anheyu.com</code>为你自己的，然后执行<code>npm run build</code>构建使用，</p><p>主题配置文件中开启<code>menu</code>中友链和朋友圈的注释，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token key atrule">友链</span><span class="token punctuation">:</span>
    <span class="token comment">#  友人帐: /link/ || anzhiyu-icon-link</span>
    <span class="token key atrule">朋友圈</span><span class="token punctuation">:</span> /fcircle/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>artstation
  <span class="token comment">#  留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token key atrule">我的</span><span class="token punctuation">:</span>
    <span class="token key atrule">音乐馆</span><span class="token punctuation">:</span> /music/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>music
  <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
  <span class="token comment">#   相册集: /album/ || anzhiyu-icon-images</span>
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token comment">#   关于本人: /about/ || anzhiyu-icon-paper-plane</span>
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/64326468190c2.png!blogimg" alt="朋友圈页" tabindex="0" loading="lazy"><figcaption>朋友圈页</figcaption></figure><h2 id="音乐馆页配置" tabindex="-1"><a class="header-anchor" href="#音乐馆页配置" aria-hidden="true">#</a> 音乐馆页配置</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo new page music
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>你会找到 <code>source/music/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;music&quot;</code></p><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span> 音乐馆
<span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2021-04-24 21:41:30</span>
<span class="token key atrule">type</span><span class="token punctuation">:</span> music
<span class="token key atrule">aplayer</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">comments</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">aside</span><span class="token punctuation">:</span> <span class="token boolean important">false</span></span>
<span class="token punctuation">---</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li><li><p>新建 <code>source/json/music.json</code>，此 json 为切换歌单按钮的歌单数据。</p><div class="language-json line-numbers-mode" data-ext="json"><pre class="language-json"><code><span class="token punctuation">[</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;青花瓷&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.4/青花瓷/青花瓷.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000002eFUFm2XYZ7z_2.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.4/青花瓷/青花瓷.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;稻香&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.1/周杰伦/稻香/稻香.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000002Neh8l0uciQZ_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.1/周杰伦/稻香/稻香.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;晴天&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/晴天/晴天.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000000MkMni19ClKG_3.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/晴天/晴天.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;七里香&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/七里香/七里香.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000003DFRzD192KKD_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/七里香/七里香.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;花海&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/花海/花海.flac&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000002Neh8l0uciQZ_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/花海/花海.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;反方向的钟&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/反方向的钟/反方向的钟.flac&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000000f01724fd7TH_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/反方向的钟/反方向的钟.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;兰亭序&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.1/周杰伦/兰亭序/兰亭序.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000002Neh8l0uciQZ_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.1/周杰伦/兰亭序/兰亭序.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;说好的辛福呢&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/说好的辛福呢/说好的辛福呢.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000002Neh8l0uciQZ_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/说好的辛福呢/说好的幸福呢.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;等你下课 (with 杨瑞代)&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.1/周杰伦/等你下课/等你下课.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000003bSL0v4bpKAx_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.1/周杰伦/等你下课/等你下课.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;我落泪情绪零碎&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/我落泪情绪零碎/我落泪情绪零碎.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000000bviBl4FjTpO_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/我落泪情绪零碎/我落泪情绪零碎.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;听妈妈的话&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/听妈妈的话/听妈妈的话.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000002jLGWe16Tf1H_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.2/听妈妈的话/听妈妈的话.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;明明就&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/明明就/明明就.flac&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000003Ow85E3pnoqi_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/明明就/明明就.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;我是如此相信&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/我是如此相信/我是如此相信.flac&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000001hGx1Z0so1YX_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music-jay@1.0.1/我是如此相信/我是如此相信.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;发如雪&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.3/发如雪/发如雪.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M0000024bjiL2aocxT_3.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.3/发如雪/发如雪.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;以父之名&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.3/以父之名/以父之名.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000000MkMni19ClKG_3.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.3/以父之名/以父之名.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;园游会&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.3/园游会/园游会.flac&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000003DFRzD192KKD_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.3/园游会/园游会.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;本草纲目&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.4/本草纲目/本草纲目.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000002jLGWe16Tf1H_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.4/本草纲目/本草纲目.lrc&quot;</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token punctuation">{</span>
    <span class="token property">&quot;name&quot;</span><span class="token operator">:</span> <span class="token string">&quot;龙卷风&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;artist&quot;</span><span class="token operator">:</span> <span class="token string">&quot;周杰伦&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;url&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.4/龙卷风/龙卷风.mp3&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;cover&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://y.qq.com/music/photo_new/T002R300x300M000000f01724fd7TH_1.jpg?max_age=2592000&quot;</span><span class="token punctuation">,</span>
    <span class="token property">&quot;lrc&quot;</span><span class="token operator">:</span> <span class="token string">&quot;https://npm.elemecdn.com/anzhiyu-music@1.0.4/龙卷风/龙卷风.lrc&quot;</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">]</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li><li><p>hexo 配置文件<code>_config.yml</code>中添加以下配置，注意不是主题配置文件</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># APlayer</span>
<span class="token comment"># https://github.com/MoePlayer/hexo-tag-aplayer/blob/master/docs/README-zh_cn.md</span>
<span class="token key atrule">aplayer</span><span class="token punctuation">:</span>
  <span class="token key atrule">meting</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">asset_inject</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li><li><p>主题配置文件中开启<code>menu</code>中我的和音乐馆的注释，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token key atrule">友链</span><span class="token punctuation">:</span>
    <span class="token key atrule">友人帐</span><span class="token punctuation">:</span> /link/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>link
  <span class="token comment">#   朋友圈: /fcircle/ || anzhiyu-icon-artstation</span>
  <span class="token comment">#   留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token key atrule">我的</span><span class="token punctuation">:</span>
    <span class="token key atrule">音乐馆</span><span class="token punctuation">:</span> /music/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>music
  <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
  <span class="token comment">#   相册集: /album/ || anzhiyu-icon-images</span>
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token comment">#   关于本人: /about/ || anzhiyu-icon-paper-plane</span>
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li></ol><blockquote><p>如何修改默认歌单?</p></blockquote><p>将<code>menu</code>中音乐馆的路径修改为以下格式即可<code>/music/?id=1708664797&amp;server=tencent</code>，支持<code>id</code>和<code>server</code>参数。</p><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/643264b4da332.png!blogimg" alt="音乐馆页" tabindex="0" loading="lazy"><figcaption>音乐馆页</figcaption></figure><h2 id="_404-页面" tabindex="-1"><a class="header-anchor" href="#_404-页面" aria-hidden="true">#</a> 404 页面</h2><p>主题内置了一个简单的 404 页面，可在设置中开启</p><div class="hint-container warning"><p class="hint-container-title">警告</p><p>本地预览时，访问出错的网站是不会跳到 404 页面的。 如需本地预览，请访问 http://localhost:4000/404.html</p></div><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># A simple 404 page</span>
<span class="token key atrule">error_404</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">subtitle</span><span class="token punctuation">:</span> <span class="token string">&quot;页面没有找到&quot;</span>
  <span class="token key atrule">background</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/64326263a9eda.png!blogimg" alt="404页面" tabindex="0" loading="lazy"><figcaption>404页面</figcaption></figure><h2 id="追番页面" tabindex="-1"><a class="header-anchor" href="#追番页面" aria-hidden="true">#</a> 追番页面</h2><p>在博客根目录执行</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">npm</span> <span class="token function">install</span> hexo-bilibili-bangumi <span class="token parameter variable">--save</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>在 hexo 配置文件<code>_config.yml</code>中加入以下配置，注意不是主题配置文件，更多配置请参考<a href="https://github.com/HCLonely/hexo-bilibili-bangumi" target="_blank" rel="noopener noreferrer">hexo-bilibili-bangumi<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># 追番插件</span>
<span class="token comment"># https://github.com/HCLonely/hexo-bilibili-bangumi</span>
<span class="token key atrule">bangumi</span><span class="token punctuation">:</span> <span class="token comment"># 追番设置</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">source</span><span class="token punctuation">:</span> bili
  <span class="token key atrule">path</span><span class="token punctuation">:</span>
  <span class="token key atrule">vmid</span><span class="token punctuation">:</span> <span class="token number">372204786</span>
  <span class="token key atrule">title</span><span class="token punctuation">:</span> <span class="token string">&quot;追番列表&quot;</span>
  <span class="token key atrule">quote</span><span class="token punctuation">:</span> <span class="token string">&quot;生命不息，追番不止！&quot;</span>
  <span class="token key atrule">show</span><span class="token punctuation">:</span> <span class="token number">1</span>
  <span class="token key atrule">lazyload</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">loading</span><span class="token punctuation">:</span>
  <span class="token key atrule">showMyComment</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">pagination</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">metaColor</span><span class="token punctuation">:</span>
  <span class="token key atrule">color</span><span class="token punctuation">:</span>
  <span class="token key atrule">webp</span><span class="token punctuation">:</span>
  <span class="token key atrule">progress</span><span class="token punctuation">:</span>
  <span class="token key atrule">extraOrder</span><span class="token punctuation">:</span>
  <span class="token key atrule">proxy</span><span class="token punctuation">:</span>
    <span class="token key atrule">host</span><span class="token punctuation">:</span> <span class="token string">&quot;代理host&quot;</span>
    <span class="token key atrule">port</span><span class="token punctuation">:</span> <span class="token string">&quot;代理端口&quot;</span>
  <span class="token key atrule">extra_options</span><span class="token punctuation">:</span>
    <span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
    <span class="token key atrule">lazyload</span><span class="token punctuation">:</span>
      <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/643264bec3298.png!blogimg" alt="追番页" tabindex="0" loading="lazy"><figcaption>追番页</figcaption></figure><h2 id="留言板页面" tabindex="-1"><a class="header-anchor" href="#留言板页面" aria-hidden="true">#</a> 留言板页面</h2><p>在博客根目录执行</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">npm</span> <span class="token function">install</span> hexo-butterfly-envelope <span class="token parameter variable">--save</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>在站点配置文件<code>_config.yml</code>中添加以下内容配置，更多配置请查看<a href="https://akilar.top/posts/e2d3c450/" target="_blank" rel="noopener noreferrer">信笺样式留言板<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># envelope_comment</span>
<span class="token comment"># see https://akilar.top/posts/e2d3c450/</span>
<span class="token key atrule">envelope_comment</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment">#控制开关</span>
  <span class="token key atrule">custom_pic</span><span class="token punctuation">:</span>
    <span class="token key atrule">cover</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/hexo<span class="token punctuation">-</span>butterfly<span class="token punctuation">-</span>envelope/lib/violet.jpg <span class="token comment">#信笺头部图片</span>
    <span class="token key atrule">line</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/hexo<span class="token punctuation">-</span>butterfly<span class="token punctuation">-</span>envelope/lib/line.png <span class="token comment">#信笺底部图片</span>
    <span class="token key atrule">beforeimg</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/hexo<span class="token punctuation">-</span>butterfly<span class="token punctuation">-</span>envelope/lib/before.png <span class="token comment"># 信封前半部分</span>
    <span class="token key atrule">afterimg</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/hexo<span class="token punctuation">-</span>butterfly<span class="token punctuation">-</span>envelope/lib/after.png <span class="token comment"># 信封后半部分</span>
  <span class="token key atrule">message</span><span class="token punctuation">:</span> <span class="token comment">#信笺正文，多行文本，写法如下</span>
    <span class="token punctuation">-</span> 有什么想问的？
    <span class="token punctuation">-</span> 有什么想说的？
    <span class="token punctuation">-</span> 有什么想吐槽的？
    <span class="token punctuation">-</span> 哪怕是有什么想吃的，都可以告诉我哦~
  <span class="token key atrule">bottom</span><span class="token punctuation">:</span> 自动书记人偶竭诚为您服务！ <span class="token comment">#仅支持单行文本</span>
  <span class="token key atrule">height</span><span class="token punctuation">:</span> <span class="token comment">#1024px，信封划出的高度</span>
  <span class="token key atrule">path</span><span class="token punctuation">:</span> <span class="token comment">#【可选】comments 的路径名称。默认为 comments，生成的页面为 comments/index.html</span>
  <span class="token key atrule">front_matter</span><span class="token punctuation">:</span> <span class="token comment">#【可选】comments页面的 front_matter 配置</span>
    <span class="token key atrule">title</span><span class="token punctuation">:</span> 留言板
    <span class="token key atrule">comments</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
    <span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
    <span class="token key atrule">type</span><span class="token punctuation">:</span> envelope
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/09/643264eb13666.png!blogimg" alt="留言板" tabindex="0" loading="lazy"><figcaption>留言板</figcaption></figure><h2 id="我的装备页面" tabindex="-1"><a class="header-anchor" href="#我的装备页面" aria-hidden="true">#</a> 我的装备页面</h2><ol><li><p>前往你的 Hexo 博客的根目录</p></li><li><p>在 Hexo 博客根目录 <code>[blog]</code>下打开终端，输入</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo new page equipment
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>你会找到 <code>source/equipment/index.md</code> 这个文件</p></li><li><p>修改这个文件： 记得添加 <code>type: &quot;equipment&quot;</code></p><div class="language-markdown line-numbers-mode" data-ext="md"><pre class="language-markdown"><code><span class="token front-matter-block"><span class="token punctuation">---</span>
<span class="token front-matter yaml language-yaml"><span class="token key atrule">title</span><span class="token punctuation">:</span> 我的装备
<span class="token key atrule">date</span><span class="token punctuation">:</span> <span class="token datetime number">2023-06-10 21:33:24</span>
<span class="token key atrule">type</span><span class="token punctuation">:</span> equipment
<span class="token key atrule">aside</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
<span class="token key atrule">top_img</span><span class="token punctuation">:</span> <span class="token boolean important">false</span></span>
<span class="token punctuation">---</span></span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></li><li><p>添加数据，新建文件<code>[blog]\source\_data\equipment.yml</code>,没有<code>_data</code>文件夹的话也请自己新建。以下是默认格式示例，打开<code>[blog]\source\_data\equipment.yml</code>，输入：</p></li></ol><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token punctuation">-</span> <span class="token key atrule">class_name</span><span class="token punctuation">:</span> 好物
  <span class="token key atrule">description</span><span class="token punctuation">:</span> 实物装备推荐
  <span class="token key atrule">tip</span><span class="token punctuation">:</span> 跟 安知鱼 一起享受科技带来的乐趣
  <span class="token key atrule">top_background</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c38842b7a.webp
  <span class="token key atrule">good_things</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">title</span><span class="token punctuation">:</span> 生产力
      <span class="token key atrule">description</span><span class="token punctuation">:</span> 提升自己生产效率的硬件设备
      <span class="token key atrule">equipment_list</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> MacBook Pro 2021 16 英寸
          <span class="token key atrule">specification</span><span class="token punctuation">:</span> M1 Max 64G / 1TB
          <span class="token key atrule">description</span><span class="token punctuation">:</span> 屏幕显示效果好、色彩准确、对比度强、性能强劲、续航优秀。可以用来开发和设计。
          <span class="token key atrule">image</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c3b191e2e.png
          <span class="token key atrule">link</span><span class="token punctuation">:</span> /posts/571d.html
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> iPad 2020
          <span class="token key atrule">specification</span><span class="token punctuation">:</span> 深空灰 / 128G
          <span class="token key atrule">description</span><span class="token punctuation">:</span> 事事玩得转，买前生产力，买后爱奇艺。
          <span class="token key atrule">image</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c3b191e2e.png
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.apple.com.cn/ipad<span class="token punctuation">-</span>10.2/
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> iPhone 12 mini
          <span class="token key atrule">specification</span><span class="token punctuation">:</span> 绿色 / 128G
          <span class="token key atrule">description</span><span class="token punctuation">:</span> 超瓷晶面板，玻璃背板搭配铝金属边框，曲线优美的圆角设计，mini大小正好一只手就抓住，深得我心，唯一缺点大概就是续航不够。
          <span class="token key atrule">image</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c3ded6319.webp
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.apple.com.cn/iphone<span class="token punctuation">-</span>12/specs/
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> AirPods（第三代）
          <span class="token key atrule">specification</span><span class="token punctuation">:</span> 标准版
          <span class="token key atrule">description</span><span class="token punctuation">:</span> 第三代对比第二代提升很大，和我一样不喜欢入耳式耳机的可以入，空间音频等功能确实新颖，第一次使用有被惊艳到。
          <span class="token key atrule">image</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c3ded6319.webp
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.apple.com.cn/airpods<span class="token punctuation">-</span>3rd<span class="token punctuation">-</span>generation/
    <span class="token punctuation">-</span> <span class="token key atrule">title</span><span class="token punctuation">:</span> 出行
      <span class="token key atrule">description</span><span class="token punctuation">:</span> 用来出行的实物及设备
      <span class="token key atrule">equipment_list</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> Apple Watch Series 8
          <span class="token key atrule">specification</span><span class="token punctuation">:</span> 黑色
          <span class="token key atrule">description</span><span class="token punctuation">:</span> 始终为我的健康放哨，深夜弹出站立提醒，不过确实有效的提高了我的运动频率，配合apple全家桶还是非常棒的产品，缺点依然是续航。
          <span class="token key atrule">image</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c40ab698a.webp
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//www.apple.com.cn/apple<span class="token punctuation">-</span>watch<span class="token punctuation">-</span>series<span class="token punctuation">-</span>8/
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> NATIONAL GEOGRAPHIC双肩包
          <span class="token key atrule">specification</span><span class="token punctuation">:</span> 黑色
          <span class="token key atrule">description</span><span class="token punctuation">:</span> 国家地理黑色大包，正好装下16寸 Macbook Pro，并且背起来很舒适，底部自带防雨罩也好用，各种奇怪的小口袋深得我心。
          <span class="token key atrule">image</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c40ab698a.webp
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//item.jd.com/100011269828.html
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> NATIONAL GEOGRAPHIC学生书包🎒
          <span class="token key atrule">specification</span><span class="token punctuation">:</span> 红白色
          <span class="token key atrule">description</span><span class="token punctuation">:</span> 国家地理黑色大包，冰冰🧊同款，颜值在线且实用。
          <span class="token key atrule">image</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//bu.dusays.com/2023/07/05/64a4c40ab698a.webp
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//item.jd.com/100005889786.html
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>主题配置文件中开启<code>menu</code>中关于和我的装备的注释，导航栏我的装备，注意缩进！！！</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token comment"># 文章:</span>
  <span class="token comment">#   隧道: /archives/ || anzhiyu-icon-box-archive</span>
  <span class="token comment">#   分类: /categories/ || anzhiyu-icon-shapes</span>
  <span class="token comment">#   标签: /tags/ || anzhiyu-icon-tags</span>

  <span class="token comment"># 友链:</span>
  <span class="token comment">#   友人帐: /link/ || anzhiyu-icon-link</span>
  <span class="token comment">#   朋友圈: /fcircle/ || anzhiyu-icon-artstation</span>
  <span class="token comment">#   留言板: /comments/ || anzhiyu-icon-envelope</span>

  <span class="token comment"># 我的:</span>
  <span class="token comment">#   音乐馆: /music/ || anzhiyu-icon-music</span>
  <span class="token comment">#   追番页: /bangumis/ || anzhiyu-icon-bilibili</span>
  <span class="token comment">#   相册集: /album/ || anzhiyu-icon-images</span>
  <span class="token comment">#   小空调: /air-conditioner/ || anzhiyu-icon-fan</span>

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token comment">#   关于本人: /about/ || anzhiyu-icon-paper-plane</span>
    <span class="token comment">#   闲言碎语: /essay/ || anzhiyu-icon-lightbulb</span>
    <span class="token comment">#   随便逛逛: javascript:toRandomPost() || anzhiyu-icon-shoe-prints1</span>
    <span class="token key atrule">我的装备</span><span class="token punctuation">:</span> /equipment/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>dice<span class="token punctuation">-</span>d20
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><div class="hint-container warning"><p class="hint-container-title">警告</p><p>示例数据中的图片不保证可用性，请自行更换为您自己的图床链接。图床相关知识=&gt;<a href="https://blog.anheyu.com/posts/2785.html" target="_blank" rel="noopener noreferrer">我的图床方案<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div></div><!--[--><!--]--></div><footer class="page-meta"><!----><div class="meta-item last-updated"><span class="meta-item-label">上次更新: </span><!----></div><div class="meta-item contributors"><span class="meta-item-label">贡献者: </span><span class="meta-item-info"><!--[--><!--[--><span class="contributor" title="email: <EMAIL>">anzhiyu</span><!----><!--]--><!--]--></span></div></footer><nav class="page-nav"><p class="inner"><span class="prev"><a href="/anzhiyu-docs/quick-start.html" class="" aria-label="快速上手"><!--[--><!--]--> 快速上手 <!--[--><!--]--></a></span><span class="next"><a href="/anzhiyu-docs/site-configuration1.html" class="" aria-label="站点配置(一)"><!--[--><!--]--> 站点配置(一) <!--[--><!--]--></a></span></p></nav><!--[--><!--]--></main><!--]--></div><!----><!--]--></div>
    <script type="module" src="/anzhiyu-docs/assets/app-18744df2.js" defer></script>
  </body>
</html>
