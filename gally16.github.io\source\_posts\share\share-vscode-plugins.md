---
title: 分享VSCode插件
subtitle: Share Vscode Plugins Here
author: geekswg
description: 摘要
keywords: [geekswg,毕少侠,分享]

tags:
  - 分享
  - vscode
  - plugins
categories:
  - share
featuredImage: /images/posts/featured-image-preview.jpg
ai:
  - >-
    所有分享资源来自互联网，如有侵权请告知，会立即删除> 分享 VSCode
    插件，提升编码效率，插件侠篇。小技巧：vscode插件栏目中，热门标签类目下的插件都是比较优秀和好用的插件，不知道用什么插件时去哪里可以发现很多宝藏的插件！推荐里面也能找到你想要的，微软这方面做的还是非常不错的。
  - 由人工Geek ChatGPT生成摘要内容。
abbrlink: 61d5
date: 2023-03-04 07:34:45
---

所有分享资源来自互联网，如有侵权请告知，会立即删除

> 分享 VSCode 插件，提升编码效率，插件侠篇。

小技巧：vscode插件栏目中，热门标签类目下的插件都是比较优秀和好用的插件，不知道用什么插件时去哪里可以发现很多宝藏的插件！推荐里面也能找到你想要的，微软这方面做的还是非常不错的。

<!--more-->
## AI类-辅助写代码工具

|<center>**插件链接**</center>| **描述** | **备注** |
|:--|:------|:--|
|[CodeGeex](https://marketplace.visualstudio.com/items?itemName=aminer.codegeex)|免费的AI编程助手、代码自动生成、代码智能补全、代码翻译、支持多种语言、适配主流IDE|国产，免费|
|[微软IntelliCode](https://marketplace.visualstudio.com/items?itemName=VisualStudioExptTeam.vscodeintellicode)|AI-assisted development|微软自家的|
|[GitHub Copilot](https://marketplace.visualstudio.com/items?itemName=GitHub.copilot)|Your AI pair programmer|代码能力比较强|

## tools-工具类


## git功能增强

> vscode自带的git插件只能提交和更新代码，不能查看和查看远程的分支历史提交记录。

|链接|描述|备注|
|:--|:------|:--|
|[Git History](https://marketplace.visualstudio.com/items?itemName=donjayamanne.githistory)|View git log, file history, compare branches or commits|查看git提交记录和比较提交信息|
