# Woiden VPS 自动续期工具发布脚本

Write-Host "🚀 开始发布 Woiden VPS 自动续期工具..." -ForegroundColor Green
Write-Host ""

# 检查git状态
Write-Host "📝 检查git状态..." -ForegroundColor Yellow
git status
Write-Host ""

# 添加所有更改
Write-Host "📦 添加所有更改到暂存区..." -ForegroundColor Yellow
git add -A
Write-Host ""

# 检查暂存的更改
Write-Host "📝 检查暂存的更改..." -ForegroundColor Yellow
git status --short
Write-Host ""

# 提交更改
Write-Host "💾 提交更改..." -ForegroundColor Yellow
$commitMessage = @"
🚀 重构项目：清理冗余文件，完善Bot Token支持

主要更改：
✨ 新功能
- 添加完整的Telegram Bot Token支持
- 新增快速启动菜单 (quick_start.py)
- 添加Bot Token创建指南 (BOT_TOKEN_GUIDE.md)
- 新增项目结构说明 (PROJECT_STRUCTURE.md)

🔧 改进
- 重构多账户续期系统
- 优化GitHub Actions配置
- 完善错误处理和日志记录
- 支持非交互式环境运行

🗑️ 清理
- 删除过时的单账户版本文件
- 移除重复的测试工具
- 清理临时文件和缓存
- 优化项目结构

📚 文档
- 更新README.md，添加详细使用指南
- 新增SETUP_GUIDE.md详细设置指南
- 添加.gitignore防止敏感文件泄露

🛡️ 安全
- 支持Bot Token和Session两种认证方式
- 改进GitHub Actions环境变量处理
- 增强配置验证和错误提示
"@

git commit -m $commitMessage
Write-Host ""

# 推送到GitHub
Write-Host "🌐 推送到GitHub..." -ForegroundColor Yellow
git push origin master
Write-Host ""

Write-Host "✅ 发布完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 接下来的步骤：" -ForegroundColor Cyan
Write-Host "1. 访问 https://github.com/gally16/woiden-renew" -ForegroundColor White
Write-Host "2. 检查GitHub Actions是否正常" -ForegroundColor White
Write-Host "3. 设置Repository Secrets（如README中所述）" -ForegroundColor White
Write-Host "4. 测试自动化工作流" -ForegroundColor White
Write-Host ""

Read-Host "按任意键继续..."
