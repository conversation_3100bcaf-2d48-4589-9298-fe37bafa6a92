#!/usr/bin/env python3
"""
配置测试脚本
用于验证Telegram连接和配置是否正确
"""

import os
import asyncio
import logging
import yaml
from pathlib import Path
from multi_account_renewer import MultiAccountRenewer, WoidenAccount

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_telegram_connection(account):
    """测试Telegram连接"""
    logger.info(f"测试账户 {account.name} 的Telegram连接...")
    
    try:
        await account.setup_telegram()
        
        # 测试获取用户信息
        me = await account.telegram_client.get_me()
        logger.info(f"✅ 账户 {account.name} 连接成功!")
        logger.info(f"   用户: {me.first_name} {me.last_name or ''}")
        logger.info(f"   用户名: @{me.username or 'N/A'}")
        logger.info(f"   电话: {me.phone or 'N/A'}")
        
        # 关闭连接
        await account.telegram_client.disconnect()
        return True
        
    except Exception as e:
        logger.error(f"❌ 账户 {account.name} 连接失败: {str(e)}")
        return False

async def test_configuration():
    """测试配置文件"""
    logger.info("开始测试配置...")
    
    # 检查配置文件是否存在
    if not os.path.exists('config.yaml'):
        logger.error("❌ 配置文件 config.yaml 不存在")
        logger.info("请运行以下命令之一:")
        logger.info("1. 本地环境: 复制并编辑 config.yaml")
        logger.info("2. GitHub Actions: 运行 python github_actions_config_generator.py")
        return False
    
    try:
        # 加载配置
        renewer = MultiAccountRenewer()
        logger.info(f"✅ 配置文件加载成功，找到 {len(renewer.accounts)} 个账户")
        
        # 测试每个账户的Telegram连接
        success_count = 0
        for account in renewer.accounts:
            logger.info(f"\n--- 测试账户: {account.name} ---")
            
            # 检查基本配置
            if not account.api_id or not account.api_hash:
                logger.error(f"❌ 账户 {account.name}: API ID 或 API Hash 为空")
                continue
                
            if not account.session_data:
                logger.warning(f"⚠️  账户 {account.name}: 没有会话数据，需要首次登录")
                if os.environ.get('GITHUB_ACTIONS'):
                    logger.error(f"❌ GitHub Actions环境中必须提供会话数据")
                    continue
            
            # 测试Telegram连接
            if await test_telegram_connection(account):
                success_count += 1
        
        logger.info(f"\n=== 测试结果 ===")
        logger.info(f"总账户数: {len(renewer.accounts)}")
        logger.info(f"连接成功: {success_count}")
        logger.info(f"连接失败: {len(renewer.accounts) - success_count}")
        
        if success_count == len(renewer.accounts):
            logger.info("🎉 所有账户配置正确!")
            return True
        else:
            logger.warning("⚠️  部分账户配置有问题，请检查")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试配置时出错: {str(e)}")
        return False

def test_environment():
    """测试运行环境"""
    logger.info("检查运行环境...")
    
    # 检查Python版本
    import sys
    logger.info(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = ['selenium', 'telethon', 'yaml']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {package} 已安装")
        except ImportError:
            logger.error(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查Chrome浏览器
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        driver.quit()
        logger.info("✅ Chrome浏览器可用")
        
    except Exception as e:
        logger.error(f"❌ Chrome浏览器不可用: {str(e)}")
        return False
    
    # 检查目录
    directories = ['sessions', 'screenshots', 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"✅ 目录 {directory} 已准备")
    
    return True

async def main():
    """主函数"""
    logger.info("Woiden VPS 续期配置测试")
    logger.info("=" * 50)
    
    # 测试环境
    if not test_environment():
        logger.error("环境测试失败，请解决上述问题后重试")
        return 1
    
    logger.info("\n" + "=" * 50)
    
    # 测试配置
    if await test_configuration():
        logger.info("\n🎉 所有测试通过！系统已准备就绪。")
        return 0
    else:
        logger.error("\n❌ 配置测试失败，请检查上述问题。")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
