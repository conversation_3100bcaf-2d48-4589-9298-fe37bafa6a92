@echo off
echo 🚀 开始发布 Woiden VPS 自动续期工具...
echo.

echo 📝 检查git状态...
git status
echo.

echo 📦 添加所有更改到暂存区...
git add -A
echo.

echo 📝 检查暂存的更改...
git status --short
echo.

echo 💾 提交更改...
git commit -m "🚀 重构项目：清理冗余文件，完善Bot Token支持

主要更改：
✨ 新功能
- 添加完整的Telegram Bot Token支持
- 新增快速启动菜单 (quick_start.py)
- 添加Bot Token创建指南 (BOT_TOKEN_GUIDE.md)
- 新增项目结构说明 (PROJECT_STRUCTURE.md)

🔧 改进
- 重构多账户续期系统
- 优化GitHub Actions配置
- 完善错误处理和日志记录
- 支持非交互式环境运行

🗑️ 清理
- 删除过时的单账户版本文件
- 移除重复的测试工具
- 清理临时文件和缓存
- 优化项目结构

📚 文档
- 更新README.md，添加详细使用指南
- 新增SETUP_GUIDE.md详细设置指南
- 添加.gitignore防止敏感文件泄露

🛡️ 安全
- 支持Bot Token和Session两种认证方式
- 改进GitHub Actions环境变量处理
- 增强配置验证和错误提示"
echo.

echo 🌐 推送到GitHub...
git push origin master
echo.

echo ✅ 发布完成！
echo.
echo 📋 接下来的步骤：
echo 1. 访问 https://github.com/gally16/woiden-renew
echo 2. 检查GitHub Actions是否正常
echo 3. 设置Repository Secrets（如README中所述）
echo 4. 测试自动化工作流
echo.
pause
