import{_ as u,M as r,p,q as d,R as n,t as e,N as s,V as t}from"./framework-2fd1fcd7.js";const m={},k={class:"hint-container warning"},b=n("p",{class:"hint-container-title"},"警告",-1),h={href:"https://github.com/anzhiyu-c/hexo-theme-anzhiyu/releases/tag/1.4.0",target:"_blank",rel:"noopener noreferrer"},_=n("h2",{id:"数学公式",tabindex:"-1"},[n("a",{class:"header-anchor",href:"#数学公式","aria-hidden":"true"},"#"),e(" 数学公式")],-1),v=n("p",null,"不要在标题里使用 mathjax 语法，toc 目录不一定能正确显示 mathjax，可能显示 mathjax 代码",-1),g=n("p",null,"建议使用 KaTex 获得更好的效果，下文有介绍！",-1),y=n("p",null,[e("修改 "),n("code",null,"主题配置文件"),e(":")],-1),f=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"mathjax"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token comment"},"# true 表示每一页都加载mathjax.js"),e(`
  `),n("span",{class:"token comment"},"# false 需要时加载，须在使用的Markdown Front-matter 加上 mathjax: true"),e(`
  `),n("span",{class:"token key atrule"},"per_page"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),x=n("blockquote",null,[n("p",null,[e("如果 "),n("code",null,"per_page"),e(" 设为 "),n("code",null,"true"),e(",则每一页都会加载 Mathjax 服务。设为 "),n("code",null,"false"),e("，则需要在文章 "),n("code",null,"Front-matter"),e(" 添加 "),n("code",null,"mathjax: true"),e(",对应的文章才会加载 Mathjax 服务。")])],-1),w=n("p",null,[e("然后你需要修改一下默认的 "),n("code",null,"markdown"),e(" 渲染引擎来实现 MathJax 的效果。")],-1),j={href:"https://www.npmjs.com/package/hexo-renderer-kramed",target:"_blank",rel:"noopener noreferrer"},T=n("p",null,[e("以下操作在你 hexo 博客的目录下 ("),n("strong",null,"不是 Anzhiyu 的目录"),e("):")],-1),A=n("ol",null,[n("li",null,"安装插件")],-1),z=n("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[n("pre",{class:"language-bash"},[n("code",null,[n("span",{class:"token function"},"npm"),e(" uninstall hexo-renderer-marked "),n("span",{class:"token parameter variable"},"--save"),e(`
`),n("span",{class:"token function"},"npm"),e(),n("span",{class:"token function"},"install"),e(" hexo-renderer-kramed "),n("span",{class:"token parameter variable"},"--save"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),C=n("ol",{start:"2"},[n("li",null,"配置 hexo 根目录的配置文件")],-1),D=n("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[n("pre",{class:"language-bash"},[n("code",null,[e(`kramed:
  gfm: `),n("span",{class:"token boolean"},"true"),e(`
  pedantic: `),n("span",{class:"token boolean"},"false"),e(`
  sanitize: `),n("span",{class:"token boolean"},"false"),e(`
  tables: `),n("span",{class:"token boolean"},"true"),e(`
  breaks: `),n("span",{class:"token boolean"},"true"),e(`
  smartLists: `),n("span",{class:"token boolean"},"true"),e(`
  smartypants: `),n("span",{class:"token boolean"},"true"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),M=n("p",null,"不要在标题里使用 KaTeX 语法，toc 目录不能正确显示 KaTeX。",-1),N=n("p",null,[e("首先禁用 "),n("code",null,"MathJax"),e("（如果你配置过 MathJax 的话），然后修改你的"),n("code",null,"主题配置文件"),e("以便加载 "),n("code",null,"katex.min.css"),e(":")],-1),V=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"katex"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token comment"},"# true 表示每一页都加载katex.js"),e(`
  `),n("span",{class:"token comment"},"# false 需要时加载，须在使用的Markdown Front-matter 加上 katex: true"),e(`
  `),n("span",{class:"token key atrule"},"per_page"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"hide_scrollbar"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),I=n("p",null,[e("你不需要添加 "),n("code",null,"katex.min.js"),e(" 来渲染数学方程。相应的你需要卸载你之前的 hexo 的 markdown 渲染器，然后安装其它插件。")],-1),K={href:"https://github.com/KaTeX/KaTeX/tree/master/contrib/copy-tex",target:"_blank",rel:"noopener noreferrer"},W={href:"https://github.com/hexojs/hexo-renderer-markdown-it",target:"_blank",rel:"noopener noreferrer"},X=n("div",{class:"language-BASH line-numbers-mode","data-ext":"BASH"},[n("pre",{class:"language-BASH"},[n("code",null,`npm un hexo-renderer-marked --save # 如果有安装这个的话，卸载
npm un hexo-renderer-kramed --save # 如果有安装这个的话，卸载

npm i hexo-renderer-markdown-it --save # 需要安装这个渲染插件
npm install katex @renbaoshuo/markdown-it-katex #需要安装这个katex插件

`)]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),B=n("p",null,[e("在 hexo 的根目录的"),n("code",null,"_config.yml"),e(" 中配置")],-1),L=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"markdown"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"plugins"),n("span",{class:"token punctuation"},":"),e(`
    `),n("span",{class:"token punctuation"},"-"),e(),n("span",{class:"token string"},'"@renbaoshuo/markdown-it-katex"'),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),q={href:"https://katex.org/docs/options.html",target:"_blank",rel:"noopener noreferrer"},P=n("blockquote",null,[n("p",null,"注意，此方法生成的 katex 没有斜体")],-1),S=n("p",null,[e("卸载掉 marked 插件，然后安装新的"),n("code",null,"hexo-renderer-markdown-it-plus"),e(":")],-1),F=n("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[n("pre",{class:"language-bash"},[n("code",null,[n("span",{class:"token comment"},"# 替换 `hexo-renderer-kramed` 或者 `hexo-renderer-marked` 等hexo的markdown渲染器"),e(`
`),n("span",{class:"token comment"},"# 你可以在你的package.json里找到hexo的markdwon渲染器，并将其卸载"),e(`
`),n("span",{class:"token function"},"npm"),e(" un hexo-renderer-marked "),n("span",{class:"token parameter variable"},"--save"),e(`

`),n("span",{class:"token comment"},"# or"),e(`

`),n("span",{class:"token function"},"npm"),e(" un hexo-renderer-kramed "),n("span",{class:"token parameter variable"},"--save"),e(`


`),n("span",{class:"token comment"},"# 然后安装 `hexo-renderer-markdown-it-plus`"),e(`
`),n("span",{class:"token function"},"npm"),e(" i @upupming/hexo-renderer-markdown-it-plus "),n("span",{class:"token parameter variable"},"--save"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),H=n("code",null,"hexo-renderer-markdown-it-plus",-1),J=n("code",null,"@upupming/hexo-renderer-markdown-it-plus",-1),U=n("code",null,"@neilsustc/markdown-it-katex",-1),E={href:"https://github.com/yzhang-gh/vscode-markdown",target:"_blank",rel:"noopener noreferrer"},R=n("code",null,"@neilsustc/markdown-it-katex",-1),O={href:"https://katex.org/docs/options.html",target:"_blank",rel:"noopener noreferrer"},G=n("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[n("pre",{class:"language-bash"},[n("code",null,[e(`markdown_it_plus:
  plugins:
    - plugin:
      name: `),n("span",{class:"token string"},"'@neilsustc/markdown-it-katex'"),e(`
      enable: `),n("span",{class:"token boolean"},"true"),e(`
      options:
        strict: `),n("span",{class:"token boolean"},"false"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),Q=n("p",null,[e("当然，你还可以利用这个特性来定义一些自己常用的 "),n("code",null,"macros"),e("。")],-1),Y=n("h2",{id:"搜索系统",tabindex:"-1"},[n("a",{class:"header-anchor",href:"#搜索系统","aria-hidden":"true"},"#"),e(" 搜索系统")],-1),Z=n("p",null,"记得运行 hexo clean",-1),$={href:"https://github.com/LouisBarranqueiro/hexo-algoliasearch",target:"_blank",rel:"noopener noreferrer"},nn=n("code",null,"title",-1),en=n("code",null,"permalink",-1),sn=n("code",null,"content",-1),an={href:"https://github.com/LouisBarranqueiro/hexo-algoliasearch",target:"_blank",rel:"noopener noreferrer"},tn=n("p",null,"修改 主题配置文件",-1),ln=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"algolia_search"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token key atrule"},"hits"),n("span",{class:"token punctuation"},":"),e(`
    `),n("span",{class:"token key atrule"},"per_page"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token number"},"6"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),on=n("p",null,"hexo 配置文件",-1),cn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# algolia搜索: https://github.com/LouisBarranqueiro/hexo-algoliasearch"),e(`
`),n("span",{class:"token key atrule"},"algolia"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"appId"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token string"},'"xxxx"'),e(`
  `),n("span",{class:"token key atrule"},"apiKey"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token string"},'"xxxx"'),e(`
  `),n("span",{class:"token key atrule"},"adminApiKey"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token string"},'"xxxx"'),e(`
  `),n("span",{class:"token key atrule"},"chunkSize"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token number"},"5000"),e(`
  `),n("span",{class:"token key atrule"},"indexName"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token string"},'"hexo"'),e(`
  `),n("span",{class:"token key atrule"},"fields"),n("span",{class:"token punctuation"},":"),e(`
    `),n("span",{class:"token punctuation"},"-"),e(" content"),n("span",{class:"token punctuation"},":"),e("strip"),n("span",{class:"token punctuation"},":"),e("truncate"),n("span",{class:"token punctuation"},","),n("span",{class:"token number"},"0"),n("span",{class:"token punctuation"},","),n("span",{class:"token number"},"200"),e(`
    `),n("span",{class:"token punctuation"},"-"),e(" excerpt"),n("span",{class:"token punctuation"},":"),e(`strip
    `),n("span",{class:"token punctuation"},"-"),e(` gallery
    `),n("span",{class:"token punctuation"},"-"),e(` permalink
    `),n("span",{class:"token punctuation"},"-"),e(` photos
    `),n("span",{class:"token punctuation"},"-"),e(` slug
    `),n("span",{class:"token punctuation"},"-"),e(` tags
    `),n("span",{class:"token punctuation"},"-"),e(` title
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),rn=n("p",null,[e("记得运行 "),n("code",null,"hexo clean")],-1),un={href:"https://github.com/wzpan/hexo-generator-search",target:"_blank",rel:"noopener noreferrer"},pn=n("p",null,"修改 主题配置文件",-1),dn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"local_search"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"preload"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"CDN"),n("span",{class:"token punctuation"},":"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),mn=n("table",null,[n("thead",null,[n("tr",null,[n("th",null,"参数"),n("th",null,"解释")])]),n("tbody",null,[n("tr",null,[n("td",null,"enable"),n("td",null,"是否开启本地搜索")]),n("tr",null,[n("td",null,"preload"),n("td",null,"预加载，开启后，进入网页后会自动加载搜索文件。关闭时，只有点击搜索按钮后，才会加载搜索文件")]),n("tr",null,[n("td",null,"CDN"),n("td",null,"搜索文件的 CDN 地址（默认使用的本地链接）")])])],-1),kn=n("h2",{id:"分享",tabindex:"-1"},[n("a",{class:"header-anchor",href:"#分享","aria-hidden":"true"},"#"),e(" 分享")],-1),bn=n("p",null,"只能选择一个分享服务商",-1),hn={href:"https://www.addthis.com/",target:"_blank",rel:"noopener noreferrer"},_n=n("figure",null,[n("img",{src:"https://file.crazywong.com/gh/jerryc127/CDN/img/hexo-theme-butterfly-doc-addthis.jpg",alt:"",tabindex:"0",loading:"lazy"}),n("figcaption")],-1),vn=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),gn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"addThis"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(),n("span",{class:"token comment"},"# or false"),e(`
  `),n("span",{class:"token key atrule"},"pubid"),n("span",{class:"token punctuation"},":"),e(" 你的pub"),n("span",{class:"token punctuation"},"-"),e(`id
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),yn={href:"https://github.com/overtrue/share.js/",target:"_blank",rel:"noopener noreferrer"},fn=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),xn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"sharejs"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token key atrule"},"sites"),n("span",{class:"token punctuation"},":"),e(" facebook"),n("span",{class:"token punctuation"},","),e("twitter"),n("span",{class:"token punctuation"},","),e("wechat"),n("span",{class:"token punctuation"},","),e("weibo"),n("span",{class:"token punctuation"},","),e("qq "),n("span",{class:"token comment"},"#想要显示的内容"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),wn={href:"https://www.addtoany.com/",target:"_blank",rel:"noopener noreferrer"},jn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"addtoany"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token key atrule"},"item"),n("span",{class:"token punctuation"},":"),e(" facebook"),n("span",{class:"token punctuation"},","),e("twitter"),n("span",{class:"token punctuation"},","),e("wechat"),n("span",{class:"token punctuation"},","),e("sina_weibo"),n("span",{class:"token punctuation"},","),e("facebook_messenger"),n("span",{class:"token punctuation"},","),e("email"),n("span",{class:"token punctuation"},","),e(`copy_link
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),Tn=n("h2",{id:"评论",tabindex:"-1"},[n("a",{class:"header-anchor",href:"#评论","aria-hidden":"true"},"#"),e(" 评论")],-1),An=n("p",null,"开启评论需要在 comments-use 中填写你需要的评论。",-1),zn=n("p",null,"支持双评论显示，只需要配置两个评论（第一个为默认显示）",-1),Cn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"comments"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token comment"},"# Up to two comments system, the first will be shown as default"),e(`
  `),n("span",{class:"token comment"},"# Choose: Valine/Waline/Twikoo/"),e(`
  `),n("span",{class:"token key atrule"},"use"),n("span",{class:"token punctuation"},":"),e(" Twikoo"),n("span",{class:"token punctuation"},","),e("Waline "),n("span",{class:"token comment"},"# Twikoo/Waline"),e(`
  `),n("span",{class:"token key atrule"},"text"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(),n("span",{class:"token comment"},"# Display the comment name next to the button"),e(`
  `),n("span",{class:"token comment"},"# lazyload: The comment system will be load when comment element enters the browser's viewport."),e(`
  `),n("span",{class:"token comment"},"# If you set it to true, the comment count will be invalid"),e(`
  `),n("span",{class:"token key atrule"},"lazyload"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"count"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(),n("span",{class:"token comment"},"# Display comment count in post's top_img"),e(`
  `),n("span",{class:"token key atrule"},"card_post_count"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(),n("span",{class:"token comment"},"# Display comment count in Home Page"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),Dn=n("table",null,[n("thead",null,[n("tr",null,[n("th",null,"参数"),n("th",null,"解释")])]),n("tbody",null,[n("tr",null,[n("td",null,"use"),n("td",null,"使用的评论（请注意，最多支持两个，如果不需要请留空）")]),n("tr",null,[n("td",null,"text"),n("td",null,"是否显示评论服务商的名字")]),n("tr",null,[n("td",null,"lazyload"),n("td",null,"是否为评论开启 lazyload，开启后，只有滚动到评论位置时才会加载评论所需要的资源（开启 lazyload 后，评论数将不显示）")]),n("tr",null,[n("td",null,"count"),n("td",null,"是否在文章顶部显示评论数")]),n("tr",null,[n("td",null,"card_post_count"),n("td",null,"是否在首页文章卡片显示评论数")])])],-1),Mn=n("code",null,"Twikoo",-1),Nn={href:"https://cloud.tencent.com/product/tcb",target:"_blank",rel:"noopener noreferrer"},Vn={href:"https://twikoo.js.org/quick-start.html",target:"_blank",rel:"noopener noreferrer"},In=n("p",null,[e("你只需要把获取到的 "),n("code",null,"环境 ID (envId)"),e(" 填写到配置上去就行")],-1),Kn=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),Wn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# Twikoo"),e(`
`),n("span",{class:"token comment"},"# https://github.com/imaegoo/twikoo"),e(`
`),n("span",{class:"token key atrule"},"twikoo"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"envId"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"region"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"visitor"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"option"),n("span",{class:"token punctuation"},":"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),Xn=n("table",null,[n("thead",null,[n("tr",null,[n("th",null,"参数"),n("th",null,"解释")])]),n("tbody",null,[n("tr",null,[n("td",null,"envId"),n("td",null,"环境 ID")]),n("tr",null,[n("td",null,"region"),n("td",null,"环境地域，默认为 ap-shanghai，如果您的环境地域不是上海，需传此参数")]),n("tr",null,[n("td",null,"visitor"),n("td",null,"是否显示文章阅读数")]),n("tr",null,[n("td",null,"option"),n("td",null,"可选配置")]),n("tr",null,[n("td",null,"card_post_count"),n("td",null,"是否在首页文章卡片显示评论数")])])],-1),Bn=n("p",null,[e("开启 visitor 后，文章页的访问人数将改为 Twikoo 提供，而不是 "),n("code",null,"不蒜子")],-1),Ln={href:"https://github.com/xCss/Valine",target:"_blank",rel:"noopener noreferrer"},qn=n("p",null,[e("然后修改 "),n("code",null,"主题配置文件"),e(":")],-1),Pn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"valine"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"appId"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token comment"},"# leancloud application app id"),e(`
  `),n("span",{class:"token key atrule"},"appKey"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token comment"},"# leancloud application app key"),e(`
  `),n("span",{class:"token key atrule"},"avatar"),n("span",{class:"token punctuation"},":"),e(" monsterid "),n("span",{class:"token comment"},"# gravatar style https://valine.js.org/#/avatar"),e(`
  `),n("span",{class:"token key atrule"},"serverURLs"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token comment"},"# This configuration is suitable for domestic custom domain name users, overseas version will be automatically detected (no need to manually fill in)"),e(`
  `),n("span",{class:"token key atrule"},"bg"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token comment"},"# valine background"),e(`
  `),n("span",{class:"token key atrule"},"visitor"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"option"),n("span",{class:"token punctuation"},":"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),Sn=n("p",null,"开启 visitor 后，文章页的访问人数将改为 Valine 提供，而不是 不蒜子",-1),Fn=n("p",null,"Valine 于 v1.4.5 开始支持自定义表情，如果你需要自行配置，请在 emojiCDN 配置表情 CDN。",-1),Hn=n("p",null,"同时在 Hexo 工作目录下的 source/_data/创建一个 json 文件 valine.json,等同于 Valine 需要配置的 emojiMaps，valine.json 配置方式可参考如下",-1),Jn=n("p",null,"valine.json",-1),Un=n("div",{class:"language-json line-numbers-mode","data-ext":"json"},[n("pre",{class:"language-json"},[n("code",null,[n("span",{class:"token punctuation"},"{"),e(`
  `),n("span",{class:"token property"},'"tv_doge"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"6ea59c827c414b4a2955fe79e0f6fd3dcd515e24.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_亲亲"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"a8111ad55953ef5e3be3327ef94eb4a39d535d06.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_偷笑"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"bb690d4107620f1c15cff29509db529a73aee261.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_再见"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"180129b8ea851044ce71caf55cc8ce44bd4a4fc8.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_冷漠"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"b9cbc755c2b3ee43be07ca13de84e5b699a3f101.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_发怒"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"34ba3cd204d5b05fec70ce08fa9fa0dd612409ff.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_发财"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"34db290afd2963723c6eb3c4560667db7253a21a.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_可爱"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"9e55fd9b500ac4b96613539f1ce2f9499e314ed9.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_吐血"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"09dd16a7aa59b77baa1155d47484409624470c77.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_呆"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"fe1179ebaa191569b0d31cecafe7a2cd1c951c9d.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_呕吐"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"9f996894a39e282ccf5e66856af49483f81870f3.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_困"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"241ee304e44c0af029adceb294399391e4737ef2.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_坏笑"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"1f0b87f731a671079842116e0991c91c2c88645a.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_大佬"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"093c1e2c490161aca397afc45573c877cdead616.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_大哭"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"23269aeb35f99daee28dda129676f6e9ea87934f.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_委屈"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"d04dba7b5465779e9755d2ab6f0a897b9b33bb77.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_害羞"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"a37683fb5642fa3ddfc7f4e5525fd13e42a2bdb1.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_尴尬"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"7cfa62dafc59798a3d3fb262d421eeeff166cfa4.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_微笑"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"70dc5c7b56f93eb61bddba11e28fb1d18fddcd4c.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_思考"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"90cf159733e558137ed20aa04d09964436f618a1.png"'),n("span",{class:"token punctuation"},","),e(`
  `),n("span",{class:"token property"},'"tv_惊吓"'),n("span",{class:"token operator"},":"),e(),n("span",{class:"token string"},'"0d15c7e2ee58e935adc6a7193ee042388adc22af.png"'),e(`
`),n("span",{class:"token punctuation"},"}"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),En=n("p",null,"Waline - 一款从 Valine 衍生的带后端评论系统。可以将 Waline 等价成 With backend Valine。",-1),Rn={href:"https://waline.js.org/",target:"_blank",rel:"noopener noreferrer"},On=n("p",null,"然后修改 主题配置文件:",-1),Gn=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"waline"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"serverURL"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token comment"},"# Waline server address url"),e(`
  `),n("span",{class:"token key atrule"},"bg"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token comment"},"# waline background"),e(`
  `),n("span",{class:"token key atrule"},"pageview"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"option"),n("span",{class:"token punctuation"},":"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),Qn=n("p",null,"开启 pageview 后，文章页的访问人数将改为 Waline 提供，而不是 不蒜子",-1),Yn=n("h2",{id:"在线聊天",tabindex:"-1"},[n("a",{class:"header-anchor",href:"#在线聊天","aria-hidden":"true"},"#"),e(" 在线聊天")],-1),Zn=n("p",null,[e("这些工具都提供了一个按钮可以打开/关闭聊天窗口。 主题也提供了一个集合主题特色的按钮来替换这些工具本身的按钮，这个聊天按钮将会出现在右下角里。 你只需要把 "),n("code",null,"chat_btn"),e(" 打开就行。")],-1),$n=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),ne=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# Chat Button [recommend]"),e(`
`),n("span",{class:"token comment"},"# It will create a button in the bottom right corner of website, and hide the origin button"),e(`
`),n("span",{class:"token key atrule"},"chat_btn"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),ee=n("p",null,[e("为了不影响访客的体验，主题提供一个 "),n("code",null,"chat_hide_show"),e(" 配置 设为 true 后，使用工具提供的按钮时，只有向上滚动才会显示聊天按钮，向下滚动时会隐藏按钮。")],-1),se=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),ae=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# The origin chat button is displayed when scrolling up, and the button is hidden when scrolling down"),e(`
`),n("span",{class:"token key atrule"},"chat_hide_show"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),te=n("p",null,[e("如果使用工具自带的聊天按钮，按钮位置可能会遮挡右下角图标，请配置 "),n("code",null,"rightside-bottom"),e(" 调正右下角图标位置")],-1),le={href:"https://chatra.com/cn/",target:"_blank",rel:"noopener noreferrer"},oe=n("code",null,"Public key",-1),ce={href:"https://chatra.com/cn/",target:"_blank",rel:"noopener noreferrer"},ie=n("code",null,"Preferences",-1),re=n("code",null,"Public key",-1),ue=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),pe=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# chatra"),e(`
`),n("span",{class:"token comment"},"# https://chatra.io/"),e(`
`),n("span",{class:"token key atrule"},"chatra"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token key atrule"},"id"),n("span",{class:"token punctuation"},":"),e(` xxxxxxxx
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),de=n("p",null,[n("code",null,"chatra"),e(" 的样式你可以 "),n("code",null,"Chat Widget"),e(" 自行配置")],-1),me=n("p",null,[e("配置 tidio,需要知道 "),n("code",null,"Public key")],-1),ke={href:"https://www.tidio.com/",target:"_blank",rel:"noopener noreferrer"},be=n("code",null,"Preferences > Developer",-1),he=n("code",null,"Public key",-1),_e=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),ve=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# tidio"),e(`
`),n("span",{class:"token comment"},"# https://www.tidio.com/"),e(`
`),n("span",{class:"token key atrule"},"tidio"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token key atrule"},"public_key"),n("span",{class:"token punctuation"},":"),e(` XXXX
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),ge=n("p",null,[n("code",null,"tidio"),e("的样式你可以"),n("code",null,"Channels"),e("自行配置")],-1),ye={href:"https://www.daocloud.io/",target:"_blank",rel:"noopener noreferrer"},fe=n("p",null,[e("找到你的 "),n("code",null,"app id"),n("img",{src:"https://file.crazywong.com/gh/jerryc127/CDN/img/hexo-theme-butterfly-docs-chat-daovoice-appid.png",alt:"",loading:"lazy"})],-1),xe=n("p",null,[e("修改 "),n("code",null,"主题配置文件")],-1),we=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# daovoice"),e(`
`),n("span",{class:"token comment"},"# http://daovoice.io/"),e(`
`),n("span",{class:"token key atrule"},"daovoice"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"true"),e(`
  `),n("span",{class:"token key atrule"},"app_id"),n("span",{class:"token punctuation"},":"),e(` xxxxx
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),je=n("p",null,[e("可在"),n("code",null,"聊天设置"),e("里配置聊天按钮等样式")],-1),Te={href:"https://crisp.chat/en/",target:"_blank",rel:"noopener noreferrer"},Ae=n("p",null,"找到需要的网站 ID",-1),ze=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token comment"},"# crisp"),e(`
`),n("span",{class:"token comment"},"# https://crisp.chat/en/"),e(`
`),n("span",{class:"token key atrule"},"crisp"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"website_id"),n("span",{class:"token punctuation"},":"),e(` xxxxxxxx
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1),Ce=n("figure",null,[n("img",{src:"https://file.crazywong.com/gh/jerryc127/CDN/img/hexo-theme-buttefly-docs-chat-crisp.png",alt:"",tabindex:"0",loading:"lazy"}),n("figcaption")],-1),De=n("p",null,"messenger 为 Facebook 旗下的聊天服务",-1),Me={href:"https://developers.facebook.com/docs/messenger-platform/discovery/facebook-chat-plugin/",target:"_blank",rel:"noopener noreferrer"},Ne=n("div",{class:"language-yaml line-numbers-mode","data-ext":"yml"},[n("pre",{class:"language-yaml"},[n("code",null,[n("span",{class:"token key atrule"},"messenger"),n("span",{class:"token punctuation"},":"),e(`
  `),n("span",{class:"token key atrule"},"enable"),n("span",{class:"token punctuation"},":"),e(),n("span",{class:"token boolean important"},"false"),e(`
  `),n("span",{class:"token key atrule"},"pageID"),n("span",{class:"token punctuation"},":"),e(` xxxxx
  `),n("span",{class:"token key atrule"},"lang"),n("span",{class:"token punctuation"},":"),e(" zh_TW "),n("span",{class:"token comment"},"# Language en_US/zh_CN/zh_TW and so on"),e(`
`)])]),n("div",{class:"line-numbers","aria-hidden":"true"},[n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"}),n("div",{class:"line-number"})])],-1);function Ve(Ie,Ke){const a=r("ExternalLinkIcon"),i=r("Tabs");return p(),d("div",null,[n("div",k,[b,n("p",null,[e("本教程更新于 2023 年 7 月 5 日，教程的内容针对最新的 anzhiyu 主题(如果你是旧版本，教程会有出入，请留意) 🐟 安知鱼 已经更新到 "),n("a",h,[e("1.4.0"),s(a)])])]),_,s(i,{id:"8",data:[{title:"Mathjax"},{title:"KaTeX"},{title:"hexo-renderer-markdown-it"},{title:"hexo-renderer-markdown-it-plus"}]},{tab0:t(({title:l,value:o,isActive:c})=>[v,g,y,f,x,w,n("p",null,[e("查看: "),n("a",j,[e("hexo-renderer-kramed"),s(a)])]),T,A,z,C,D]),tab1:t(({title:l,value:o,isActive:c})=>[M,N,V,I,n("p",null,[e("因为 KaTeX 更快更轻量，因此没有 MathJax 的功能多（比如右键菜单）。为那些使用 MathJax 的用户，主题也内置了 katex 的 "),n("a",K,[e("复制"),s(a)]),e(" 功能。")])]),tab2:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("卸载掉 marked 插件，安装 "),n("a",W,[e("hexo-renderer-markdown-it"),s(a)])]),X,B,L,n("p",null,[e("如需配置其它参数，请参考 "),n("a",q,[e("katex 官网"),s(a)])])]),tab3:t(({title:l,value:o,isActive:c})=>[P,S,F,n("p",null,[e("注意到 "),H,e(" 已经无人持续维护, 所以我们使用 "),J,e("。 这份 fork 的代码使用了 "),U,e(" 同时它也是 VSCode 的插件 "),n("a",E,[e("Markdown All in One"),s(a)]),e(" 所使用的, 所以我们可以获得最新的 KaTex 功能例如 \\tag{}。")]),n("p",null,[e("你还可以通过 "),R,e(" 控制 KaTeX 的设置，所有可配置的选项参见 "),n("a",O,[e("https://katex.org/docs/options.html"),s(a)]),e("。 比如你想要禁用掉 KaTeX 在命令行上输出的宂长的警告信息，你可以在根目录的 _config.yml 中使用下面的配置将 strict 设置为 false")]),G,Q]),_:1}),Y,s(i,{id:"104",data:[{title:"algolia"},{title:"本地搜索"}],"tab-id":"安装搜索系统"},{tab0:t(({title:l,value:o,isActive:c})=>[Z,n("p",null,[e("使用 "),n("a",$,[e("hexo-algoliasearch"),s(a)]),e("，请记得配置 fields 参数的 "),nn,e(", "),en,e(" 和 "),sn]),n("p",null,[e("你需要安装 "),n("a",an,[e("hexo-algoliasearch"),s(a)]),e(". 根据它的说明文档去做相应的配置。")]),tn,ln,on,cn]),tab1:t(({title:l,value:o,isActive:c})=>[rn,n("p",null,[e("你需要安装 "),n("a",un,[e("hexo-generator-search"),s(a)]),e("，根据它的文档去做相应配置")]),pn,dn,mn]),_:1}),kn,bn,s(i,{id:"181",data:[{title:"AddThis"},{title:"sharejs"},{title:"addtoany"}]},{tab0:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("访问 "),n("a",hn,[e("AddThis"),s(a)]),e(" 官网 找到你的 pub-id")]),_n,vn,gn]),tab1:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("如果你不知道 "),n("a",yn,[e("sharejs"),s(a)]),e("，看看它的説明。")]),fn,xn]),tab2:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("可以到 "),n("a",wn,[e("addtoany"),s(a)]),e(" 查看使用説明")]),jn]),_:1}),Tn,s(i,{id:"213",data:[{title:"通用配置"},{title:"Twikoo"},{title:"Valine"},{title:"Waline"}]},{tab0:t(({title:l,value:o,isActive:c})=>[An,zn,Cn,Dn]),tab1:t(({title:l,value:o,isActive:c})=>[n("p",null,[Mn,e(" 是一个简洁、安全、无后端的静态网站评论系统，基于"),n("a",Nn,[e("腾讯云开发"),s(a)]),e("。")]),n("p",null,[e("具体如何配置评论，请查看 "),n("a",Vn,[e("Twikoo 文档"),s(a)])]),In,Kn,Wn,Xn,Bn]),tab2:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("遵循 "),n("a",Ln,[e("Valine"),s(a)]),e(" 的指示去配置你的 LeanCloud 应用。以及查看相应的配置说明。")]),qn,Pn,Sn,Fn,Hn,Jn,Un]),tab3:t(({title:l,value:o,isActive:c})=>[En,n("p",null,[e("具体配置可参考 "),n("a",Rn,[e("waline 文档"),s(a)])]),On,Gn,Qn]),_:1}),Yn,s(i,{id:"390",data:[{title:"通用配置"},{title:"chatra"},{title:"tidio"},{title:"daovoice"},{title:"crisp"},{title:"messenger"}]},{tab0:t(({title:l,value:o,isActive:c})=>[Zn,$n,ne,ee,se,ae,te]),tab1:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("配置 "),n("a",le,[e("chatra"),s(a)]),e(",需要知道 "),oe]),n("p",null,[e("打开 "),n("a",ce,[e("chatra"),s(a)]),e(" 并注册账号。 你可以在 "),ie,e(" 中找到 "),re]),ue,pe,de]),tab2:t(({title:l,value:o,isActive:c})=>[me,n("p",null,[e("打开 "),n("a",ke,[e("tidio"),s(a)]),e(" 并注册账号。 你可以在 "),be,e(" 中找到 "),he]),_e,ve,ge]),tab3:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("打开 "),n("a",ye,[e("daovoice"),s(a)]),e(" 和注册帐号")]),fe,xe,we,je]),tab4:t(({title:l,value:o,isActive:c})=>[n("p",null,[e("打开 "),n("a",Te,[e("crisp"),s(a)]),e(" 并注册帐号")]),Ae,ze,Ce]),tab5:t(({title:l,value:o,isActive:c})=>[De,n("p",null,[e("具体操作请查看 "),n("a",Me,[e("Facebook 洽谈附加程式 - Messenger 平台"),s(a)])]),Ne]),_:1})])}const Xe=u(m,[["render",Ve],["__file","site-configuration4.html.vue"]]);export{Xe as default};
