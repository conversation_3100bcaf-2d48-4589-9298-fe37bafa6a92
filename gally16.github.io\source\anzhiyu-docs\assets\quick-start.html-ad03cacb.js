const l=JSON.parse('{"key":"v-96f5eae0","path":"/quick-start.html","title":"","lang":"zh-CN","frontmatter":{},"headers":[{"level":2,"title":"💻 安裝","slug":"💻-安裝","link":"#💻-安裝","children":[]},{"level":2,"title":"⚙ 应用主题","slug":"⚙-应用主题","link":"#⚙-应用主题","children":[]},{"level":2,"title":"安装 pug 和 stylus 渲染插件","slug":"安装-pug-和-stylus-渲染插件","link":"#安装-pug-和-stylus-渲染插件","children":[]},{"level":2,"title":"更好的配置，便于升级主题","slug":"更好的配置-便于升级主题","link":"#更好的配置-便于升级主题","children":[]},{"level":2,"title":"本地启动 hexo","slug":"本地启动-hexo","link":"#本地启动-hexo","children":[]},{"level":2,"title":"有问题?","slug":"有问题","link":"#有问题","children":[]},{"level":2,"title":"群聊","slug":"群聊","link":"#群聊","children":[]},{"level":2,"title":"技术支持","slug":"技术支持","link":"#技术支持","children":[]},{"level":2,"title":"主题设计","slug":"主题设计","link":"#主题设计","children":[]}],"git":{"updatedTime":1688523268000,"contributors":[{"name":"anzhiyu","email":"<EMAIL>","commits":9},{"name":"Norris Scamander","email":"<EMAIL>","commits":1}]},"filePathRelative":"quick-start.md"}');export{l as data};
