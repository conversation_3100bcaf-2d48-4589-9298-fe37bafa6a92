import os
import re
import time
import base64
import yaml
import logging
import asyncio
from datetime import datetime
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from telethon import TelegramClient, events

# 移除playwright依赖，专注于selenium实现
PLAYWRIGHT_AVAILABLE = False

# 配置根日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("multi_renew_log.txt"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("woiden_multi_renewer")

class WoidenAccount:
    """单个Woiden账户的类"""
    def __init__(self, account_config, global_config):
        self.name = account_config.get('name', 'unnamed')
        self.logger = logging.getLogger(f"woiden_renewer.{self.name}")
        
        # Telegram配置
        self.telegram_config = account_config.get('telegram', {})
        self.api_id = self.telegram_config.get('api_id')
        self.api_hash = self.telegram_config.get('api_hash')
        self.session_data = self.telegram_config.get('session', '')
        self.bot_token = self.telegram_config.get('bot_token', '')  # 添加bot token支持
        self.bot_username = self.telegram_config.get('bot_username', 'HAX Bot')
        
        # Woiden配置
        self.woiden_config = account_config.get('woiden', {})
        self.vps_id = self.woiden_config.get('vps_id', '')
        
        # NopeCHA配置
        self.nopecha_config = account_config.get('nopecha', {})
        self.nopecha_extension_path = self.nopecha_config.get('extension_path', 'C:\\Users\\<USER>\\Desktop\\chromium_automation')
        self.nopecha_api_key = self.nopecha_config.get('api_key', '')
        
        # 全局配置
        self.headless = global_config.get('headless', False)
        self.screenshots = global_config.get('screenshots', True)
        
        # 运行时变量
        self.telegram_client = None
        self.verification_code = None
        self.driver = None
        self.screenshot_dir = Path(f"screenshots/{self.name}")
        
        # 创建截图目录
        if self.screenshots:
            self.screenshot_dir.mkdir(parents=True, exist_ok=True)
    
    async def setup_telegram(self):
        """设置Telegram客户端并登录"""
        self.logger.info("正在设置Telegram客户端...")

        # 检查必要的配置
        if not self.api_id or not self.api_hash:
            raise ValueError("Telegram API ID 和 API Hash 不能为空")

        session_file = f"sessions/{self.name}_session.session"

        # 创建sessions目录
        Path("sessions").mkdir(exist_ok=True)

        # 创建Telegram客户端
        self.telegram_client = TelegramClient(session_file, self.api_id, self.api_hash)

        # 连接到Telegram
        try:
            # 优先使用bot token（如果提供）
            if self.bot_token:
                self.logger.info("使用Bot Token进行连接...")
                await self.telegram_client.start(bot_token=self.bot_token)
                self.logger.info("Bot Token连接成功")
            else:
                # 使用用户会话数据
                self.logger.info("使用用户会话数据进行连接...")

                # 如果有会话数据，则保存到文件中
                if self.session_data:
                    try:
                        # 解码base64会话数据
                        session_data = base64.b64decode(self.session_data)

                        # 保存到文件
                        with open(session_file, 'wb') as f:
                            f.write(session_data)

                        self.logger.info("已从配置加载Telegram会话数据")
                    except Exception as e:
                        self.logger.error(f"加载Telegram会话数据失败: {str(e)}")
                        raise

                # 在GitHub Actions环境中，使用非交互式登录
                if os.environ.get('GITHUB_ACTIONS'):
                    self.logger.info("检测到GitHub Actions环境，使用非交互式登录")
                    if not self.session_data:
                        raise ValueError(f"账户 {self.name} 在GitHub Actions环境中必须提供有效的session数据或bot token")

                    # 直接启动，不需要用户交互
                    await self.telegram_client.start()
                else:
                    # 本地环境，允许交互式登录
                    await self.telegram_client.start()

                # 如果是首次运行且没有会话数据，则输出提示（仅在本地环境）
                if not self.session_data and not os.environ.get('GITHUB_ACTIONS'):
                    self.logger.info(f"账户 {self.name} 首次运行需要手动验证Telegram登录，请完成验证后保存会话数据")

                    # 等待Telegram客户端完成登录
                    await asyncio.sleep(5)

                    # 获取会话文件内容
                    if os.path.exists(session_file):
                        with open(session_file, 'rb') as f:
                            session_data = f.read()

                        # 转换为base64并输出
                        session_base64 = base64.b64encode(session_data).decode('utf-8')
                        self.logger.info(f"请将以下会话数据保存到配置文件中的session字段:\n{session_base64}")

            self.logger.info("Telegram客户端连接成功")

            # 监听来自HAX Bot的消息
            @self.telegram_client.on(events.NewMessage(from_users=self.bot_username))
            async def handler(event):
                message = event.message.text
                if "Your Code is" in message:
                    self.verification_code = message.split("Your Code is")[1].strip()
                    self.logger.info(f"收到验证码: {self.verification_code}")

        except Exception as e:
            self.logger.error(f"Telegram客户端连接失败: {str(e)}")
            raise
    
    def setup_browser(self):
        """设置浏览器，加载NopeCHA扩展"""
        self.logger.info("正在设置浏览器...")
        options = webdriver.ChromeOptions()
        
        # 如果配置为无头模式，则添加相应选项
        if self.headless:
            options.add_argument("--headless")
            options.add_argument("--window-size=1920,1080")
        
        # 添加必要的选项
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        # 加载NopeCHA扩展
        if os.path.exists(self.nopecha_extension_path):
            options.add_argument(f"--load-extension={self.nopecha_extension_path}")
            self.logger.info(f"已添加NopeCHA扩展: {self.nopecha_extension_path}")
        else:
            self.logger.warning(f"NopeCHA扩展路径不存在: {self.nopecha_extension_path}")
        
        # 启动Chrome浏览器
        self.driver = webdriver.Chrome(options=options)
        
        # 配置NopeCHA (如果需要设置API密钥)
        if self.nopecha_api_key:
            self.logger.info("正在配置NopeCHA扩展...")
            
            if self.headless:
                # 对于无头浏览器，直接使用localStorage设置API密钥
                script = f"""
                localStorage.setItem('apiKey', '{self.nopecha_api_key}');
                localStorage.setItem('enabled', 'true');
                localStorage.setItem('autoSubmit', 'true');
                localStorage.setItem('autoSolve', 'true');
                """
                
                # 打开任意页面以执行脚本
                self.driver.get("https://example.com")
                self.driver.execute_script(script)
            else:
                # 对于有界面的浏览器，使用选项页面设置
                self.driver.get("chrome-extension://dknlfmjaanfblgfdfebhijalfmhmjjjo/options.html")
                time.sleep(2)  # 等待选项页面加载
                
                try:
                    # 尝试设置API密钥
                    api_input = self.driver.find_element(By.ID, "apiKey")
                    api_input.clear()
                    api_input.send_keys(self.nopecha_api_key)
                    
                    # 保存设置
                    save_btn = self.driver.find_element(By.ID, "save")
                    save_btn.click()
                    self.logger.info("NopeCHA配置完成")
                except Exception as e:
                    self.logger.error(f"配置NopeCHA时出错: {str(e)}")
            
        self.logger.info("浏览器设置完成")
    
    def solve_math_captcha(self, captcha_text):
        """解析并计算数学验证码"""
        self.logger.info(f"正在解析验证码: {captcha_text}")
        # 使用正则表达式提取数字和运算符
        if '+' in captcha_text:
            a, b = map(int, captcha_text.split('+'))
            return a + b
        elif 'X' in captcha_text or 'x' in captcha_text:
            parts = re.split('X|x', captcha_text)
            a, b = map(int, parts)
            return a * b
        return None
    
    def save_screenshot(self, name):
        """保存截图"""
        if not self.screenshots or not self.driver:
            return
        
        try:
            filename = self.screenshot_dir / f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            self.driver.save_screenshot(str(filename))
            self.logger.info(f"已保存截图: {filename}")
        except Exception as e:
            self.logger.error(f"保存截图失败: {str(e)}")
    
    async def get_verification_code(self):
        """从Telegram获取验证码"""
        self.logger.info("等待Telegram验证码...")
        self.verification_code = None
        # 等待验证码接收
        timeout = 60  # 等待60秒
        start_time = time.time()
        while not self.verification_code and time.time() - start_time < timeout:
            await asyncio.sleep(1)
        
        if not self.verification_code:
            self.logger.error("未能在超时时间内接收到验证码")
            return False
        
        return self.verification_code
    
    async def renew_vps(self):
        """执行VPS续期流程"""
        self.logger.info(f"开始账户 {self.name} 的VPS续期流程")
        self.setup_browser()
        
        try:
            # 打开续期页面
            self.driver.get("https://woiden.id/vps-renew-code")
            self.logger.info("已打开续期页面")
            self.save_screenshot("step1_open_page")
            
            # 第一阶段: 填写网站地址和解答第一个验证码
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "web"))
            )
            
            # 填写网站地址
            self.driver.find_element(By.NAME, "web").send_keys("woiden.id")
            self.logger.info("已填写网站地址")
            
            # 解析并回答第一个验证码
            captcha_element = self.driver.find_element(By.CSS_SELECTOR, "label[for='answer']")
            captcha_text = captcha_element.text.split('=')[0].strip()
            answer = self.solve_math_captcha(captcha_text)
            self.driver.find_element(By.NAME, "answer").send_keys(str(answer))
            self.logger.info(f"已回答第一个验证码: {captcha_text} = {answer}")
            
            # 勾选复选框
            self.driver.find_element(By.NAME, "renew").click()
            self.logger.info("已勾选续期复选框")
            self.save_screenshot("step2_form_filled")
            
            # 提交第一阶段
            self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
            self.logger.info("已提交第一阶段")
            
            # 获取Telegram验证码
            code = await self.get_verification_code()
            if not code:
                return False
            
            # 第二阶段: 输入验证码和解答第二个验证码
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "code"))
            )
            self.save_screenshot("step3_code_page")
            
            # 输入验证码
            self.driver.find_element(By.NAME, "code").send_keys(code)
            self.logger.info("已输入Telegram验证码")
            
            # 解析并回答第二个验证码
            captcha_element = self.driver.find_element(By.CSS_SELECTOR, "label[for='answer']")
            captcha_text = captcha_element.text.split('=')[0].strip()
            answer = self.solve_math_captcha(captcha_text)
            self.driver.find_element(By.NAME, "answer").send_keys(str(answer))
            self.logger.info(f"已回答第二个验证码: {captcha_text} = {answer}")
            self.save_screenshot("step4_code_entered")
            
            # NopeCHA扩展会自动处理reCAPTCHA，只需等待足够的时间
            self.logger.info("等待NopeCHA扩展自动解决reCAPTCHA...")
            time.sleep(20)  # 给NopeCHA足够的时间解决验证码
            self.save_screenshot("step5_before_submit")
            
            # 点击续期按钮
            self.driver.find_element(By.CSS_SELECTOR, "button.btn.btn-primary").click()
            self.logger.info("已点击续期按钮")
            
            # 等待续期结果
            time.sleep(5)
            self.save_screenshot("step6_after_submit")
            
            # 检查是否续期成功
            if "successfully" in self.driver.page_source.lower():
                self.logger.info(f"账户 {self.name} VPS续期成功!")
                return True
            else:
                self.logger.error(f"账户 {self.name} VPS续期可能失败，请检查")
                return False
                
        except Exception as e:
            self.logger.error(f"续期过程中出错: {str(e)}")
            self.save_screenshot("error")
            return False
        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
                self.logger.info("已关闭浏览器")
    
    async def run(self):
        """运行完整的续期流程"""
        self.logger.info(f"=== 开始运行账户 {self.name} 的VPS续期程序 ===")
        try:
            await self.setup_telegram()
            success = await self.renew_vps()
            
            if success:
                self.logger.info(f"账户 {self.name} 续期流程成功完成")
            else:
                self.logger.error(f"账户 {self.name} 续期流程未能成功完成")
            
            # 关闭Telegram客户端
            await self.telegram_client.disconnect()
            self.logger.info(f"已关闭账户 {self.name} 的Telegram客户端")
            
            return success
        except Exception as e:
            self.logger.error(f"账户 {self.name} 运行出错: {str(e)}")
            return False
        finally:
            self.logger.info(f"=== 账户 {self.name} 的VPS续期程序运行结束 ===")


class MultiAccountRenewer:
    """多账户续期管理器"""
    def __init__(self, config_file="config.yaml"):
        self.logger = logging.getLogger("woiden_multi_renewer.manager")
        self.config_file = config_file
        self.accounts = []
        self.global_config = {}
        self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.global_config = config.get('global', {})
            
            # 加载所有账户
            for account_config in config.get('accounts', []):
                self.accounts.append(WoidenAccount(account_config, self.global_config))
            
            self.logger.info(f"成功加载 {len(self.accounts)} 个账户配置")
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            raise
    
    async def renew_all(self):
        """为所有账户执行续期流程"""
        self.logger.info(f"开始为 {len(self.accounts)} 个账户执行续期流程")
        
        results = []
        for account in self.accounts:
            self.logger.info(f"开始处理账户: {account.name}")
            try:
                success = await account.run()
                results.append((account.name, success))
                # 每个账户之间等待一段时间，避免并发请求
                await asyncio.sleep(5)
            except Exception as e:
                self.logger.error(f"处理账户 {account.name} 时出错: {str(e)}")
                results.append((account.name, False))
        
        # 打印总结
        self.logger.info("===== 续期结果摘要 =====")
        success_count = 0
        for name, success in results:
            status = "成功" if success else "失败"
            self.logger.info(f"账户 {name}: {status}")
            if success:
                success_count += 1
        
        self.logger.info(f"总计: {success_count}/{len(results)} 个账户续期成功")
        return results


# 移除playwright相关代码，专注于selenium实现


async def main():
    """主函数"""
    renewer = MultiAccountRenewer()
    await renewer.renew_all()


if __name__ == "__main__":
    asyncio.run(main()) 