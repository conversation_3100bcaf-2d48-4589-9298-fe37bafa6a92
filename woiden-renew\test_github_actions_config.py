#!/usr/bin/env python3
"""
GitHub Actions配置测试脚本
用于验证环境变量和配置生成是否正确
"""

import os
import logging
from github_actions_config_generator import generate_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_environment_variables():
    """测试环境变量设置"""
    logger.info("检查环境变量...")
    
    # 必需的基础变量
    required_base = [
        'TELEGRAM_API_ID_1', 'TELEGRAM_API_HASH_1',
        'TELEGRAM_API_ID_2', 'TELEGRAM_API_HASH_2'
    ]
    
    # 认证变量（至少需要一种）
    auth_vars = [
        ('TELEGRAM_SESSION_1', 'TELEGRAM_BOT_TOKEN1'),
        ('TELEGRAM_SESSION_2', 'TELEGRAM_BOT_TOKEN2')
    ]
    
    missing_base = []
    for var in required_base:
        if not os.environ.get(var):
            missing_base.append(var)
    
    if missing_base:
        logger.error(f"❌ 缺少基础环境变量: {missing_base}")
        return False
    else:
        logger.info("✅ 基础环境变量检查通过")
    
    # 检查认证变量
    auth_ok = True
    for i, (session_var, token_var) in enumerate(auth_vars, 1):
        session = os.environ.get(session_var)
        token = os.environ.get(token_var)
        
        if not session and not token:
            logger.error(f"❌ 账户{i}缺少认证信息: 需要设置 {session_var} 或 {token_var}")
            auth_ok = False
        elif token:
            logger.info(f"✅ 账户{i}: 使用Bot Token认证")
        elif session:
            logger.info(f"✅ 账户{i}: 使用Session认证")
    
    return auth_ok

def test_config_generation():
    """测试配置生成"""
    logger.info("测试配置文件生成...")
    
    try:
        success = generate_config()
        if success:
            logger.info("✅ 配置文件生成成功")
            
            # 验证生成的配置文件
            import yaml
            with open('config.yaml', 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            accounts = config.get('accounts', [])
            logger.info(f"✅ 配置包含 {len(accounts)} 个账户")
            
            for account in accounts:
                name = account['name']
                telegram = account['telegram']
                
                if telegram.get('bot_token'):
                    logger.info(f"✅ {name}: 配置了Bot Token")
                elif telegram.get('session'):
                    logger.info(f"✅ {name}: 配置了Session数据")
                else:
                    logger.error(f"❌ {name}: 缺少认证配置")
                    return False
            
            return True
        else:
            logger.error("❌ 配置文件生成失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 配置生成测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("GitHub Actions配置测试")
    logger.info("=" * 50)
    
    # 检查是否在GitHub Actions环境中
    if os.environ.get('GITHUB_ACTIONS'):
        logger.info("✅ 检测到GitHub Actions环境")
    else:
        logger.info("ℹ️  本地测试环境")
    
    # 测试环境变量
    env_ok = test_environment_variables()
    
    logger.info("\n" + "=" * 50)
    
    # 测试配置生成
    config_ok = test_config_generation()
    
    logger.info("\n" + "=" * 50)
    logger.info("测试结果:")
    
    if env_ok and config_ok:
        logger.info("🎉 所有测试通过！GitHub Actions配置正确")
        return 0
    else:
        logger.error("❌ 测试失败，请检查配置")
        logger.info("\n解决方案:")
        logger.info("1. 确保在GitHub仓库的Settings -> Secrets中设置了所有必需的变量")
        logger.info("2. 每个账户至少需要设置Bot Token或Session数据")
        logger.info("3. 参考BOT_TOKEN_GUIDE.md创建Telegram机器人")
        return 1

if __name__ == "__main__":
    exit(main())
