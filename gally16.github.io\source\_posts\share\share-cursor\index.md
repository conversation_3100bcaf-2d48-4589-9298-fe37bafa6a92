---
title: Share Cursor
subtitle: ''
author: 毕少侠
description: '让AI帮你写代码！，Cursor,程序员的好帮手'
keywords:
  - ''
tags:
  - AI
  - Cursor
  - ChatGPT
categories:
  - share
abbrlink: e71d
date: 2023-03-23 10:15:27
---
![Cursor Home](/imgs/posts/230323185021-cursor-home.png)
> [Cursor](https://www.cursor.so/)
> 最重要的是这个是 开源免费，国内直接访问的AI助手小工具.  
> 开源地址 https://github.com/getcursor/cursor

Build Software. Fast.
Write, edit, and chat about your code with a pful AI

## 食用方法

1. 快捷键：`Ctrl + K` 输入你的描述，自然语言对话，他就能根据你的描述要求开始写代码了，如果实现代码比较长的话，他可能不会一次性写完代码，写道一半停止了，你继续按快捷键告诉他继续写就可以了。

2. 快捷键 `Ctrl + L` 可以和他自然聊天，比如问题某段代码什么意思，或者问他写的代码如何运行，经过测试，他的回答都十分的智能，而且上下文理解的非常顺畅，就像和一个助理在正常聊天。

### 食用预览

* `Ctrl + K` 让他帮你写代码
![Cursor Home](/imgs/posts/230323185021-cursor1.png)
![Cursor Home](/imgs/posts/230323185021-cursor2.png)

* `Ctrl + L` 帮助你运行程序，有什么问题都可以直接问他，比如让如何安装依赖，翻译代码，解释代码的含义。
![Cursor Home](/imgs/posts/230323185021-cursor3.png)

> 总结下来，使用虽然比较简单，但是还是需要有一定的编程思维和判断能力，因为在测试的时候，使用pthon代码的时候用到了一个库，调用库的时候居然自己创造了一个方法。我在调试的运行他帮我写的代码时。居然报错了，说不存在这个方法，我去搜下这个库，确实没发现他使用的这个方法！让我哭笑不得，一方面说明这个代码应该是AI自己借鉴别人代码，如何根据我的需求，他自己编写的。2.说明这个程序呀有时候会胡诌一些不存在的东西，或者自己创造一些，但是肯定有问题，需要自己判断去修改。 

整体使用下来，效果令人吃惊，值得尝试一下。  
[官方地址](https://www.cursor.so/)
