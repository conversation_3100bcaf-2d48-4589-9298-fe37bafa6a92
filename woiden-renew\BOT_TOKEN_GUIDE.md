# Telegram Bot Token 设置指南

## 概述

使用Bot Token比用户会话数据更适合自动化场景，因为：
- 不需要交互式登录
- 更稳定，不会过期
- 专门为自动化设计

## 创建Telegram Bot

### 第一步：联系BotFather

1. 在Telegram中搜索 `@BotFather`
2. 发送 `/start` 开始对话
3. 发送 `/newbot` 创建新机器人

### 第二步：设置机器人信息

1. **机器人名称**：输入机器人的显示名称（例如：`Woiden Renewer Bot 1`）
2. **机器人用户名**：输入机器人的用户名，必须以`bot`结尾（例如：`woiden_renewer1_bot`）

### 第三步：获取Bot Token

创建成功后，BotFather会发送一条包含Bot Token的消息：
```
Use this token to access the HTTP API:
1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
```

**重要**：请妥善保存这个Token，它就是你的`TELEGRAM_BOT_TOKEN`

### 第四步：配置机器人权限

发送以下命令给BotFather来配置机器人：

1. **允许机器人接收所有消息**：
   ```
   /setprivacy
   选择你的机器人
   选择 Disable
   ```

2. **设置机器人描述**（可选）：
   ```
   /setdescription
   选择你的机器人
   输入描述：Woiden VPS自动续期机器人
   ```

## 为多个账户创建多个机器人

如果你有多个Woiden账户，需要为每个账户创建单独的机器人：

### 账户1的机器人
- 名称：`Woiden Renewer Bot 1`
- 用户名：`woiden_renewer1_bot`
- Token：保存为 `TELEGRAM_BOT_TOKEN1`

### 账户2的机器人
- 名称：`Woiden Renewer Bot 2`
- 用户名：`woiden_renewer2_bot`
- Token：保存为 `TELEGRAM_BOT_TOKEN2`

## 在GitHub Actions中设置

在GitHub仓库的 `Settings` -> `Secrets and variables` -> `Actions` 中添加：

### 必需的Secrets（Bot Token模式）：

**账户1:**
- `TELEGRAM_API_ID_1`: Telegram API ID
- `TELEGRAM_API_HASH_1`: Telegram API Hash
- `TELEGRAM_BOT_TOKEN1`: 账户1的Bot Token

**账户2:**
- `TELEGRAM_API_ID_2`: Telegram API ID
- `TELEGRAM_API_HASH_2`: Telegram API Hash
- `TELEGRAM_BOT_TOKEN2`: 账户2的Bot Token

### 可选的Secrets：
- `NOPECHA_API_KEY`: NopeCHA API密钥

## 测试Bot Token

你可以使用以下Python脚本测试Bot Token是否有效：

```python
import asyncio
from telethon import TelegramClient

async def test_bot_token():
    api_id = "YOUR_API_ID"
    api_hash = "YOUR_API_HASH"
    bot_token = "YOUR_BOT_TOKEN"
    
    client = TelegramClient('test_bot', api_id, api_hash)
    
    try:
        await client.start(bot_token=bot_token)
        me = await client.get_me()
        print(f"Bot连接成功: {me.username}")
        await client.disconnect()
        return True
    except Exception as e:
        print(f"Bot连接失败: {e}")
        return False

# 运行测试
asyncio.run(test_bot_token())
```

## 注意事项

1. **安全性**：Bot Token具有完全的机器人权限，请妥善保管
2. **唯一性**：每个机器人的Token都是唯一的，不能重复使用
3. **重置**：如果Token泄露，可以通过BotFather的`/revoke`命令重置
4. **权限**：确保机器人有接收消息的权限（通过`/setprivacy`设置）

## 优势对比

### Bot Token模式 vs 用户Session模式

| 特性 | Bot Token | 用户Session |
|------|-----------|-------------|
| 设置复杂度 | 简单 | 复杂 |
| 稳定性 | 高（不过期） | 中（可能过期） |
| 交互式登录 | 不需要 | 需要 |
| 适用场景 | 自动化 | 个人使用 |
| 安全性 | 高 | 中 |

## 故障排除

### 常见问题

1. **"Bot token invalid"**
   - 检查Token是否正确复制
   - 确认Token没有多余的空格或字符

2. **"Bot can't receive messages"**
   - 使用`/setprivacy`命令设置为Disable
   - 确认机器人已正确创建

3. **"API ID/Hash mismatch"**
   - 确认API ID和Hash来自同一个Telegram应用

推荐使用Bot Token模式，因为它更适合GitHub Actions等自动化环境。
