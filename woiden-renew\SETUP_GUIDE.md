# Woiden VPS 自动续期设置指南

## 概述

这个项目可以自动为多个Woiden VPS账户执行续期操作，支持在GitHub Actions中运行。

## 前置要求

1. **Telegram账户** - 用于接收验证码
2. **Telegram API凭据** - 从 https://my.telegram.org 获取
3. **GitHub账户** - 用于运行自动化脚本
4. **NopeCHA API密钥** (可选) - 用于自动解决验证码

## 设置步骤

### 第一步：获取Telegram API凭据

1. 访问 https://my.telegram.org
2. 使用您的手机号码登录
3. 进入 "API development tools"
4. 创建新应用，记录下：
   - `api_id` (数字)
   - `api_hash` (字符串)

### 第二步：选择认证方式

有两种认证方式可选：

#### 方式A：使用Bot Token（推荐）

1. 按照 `BOT_TOKEN_GUIDE.md` 创建Telegram机器人
2. 获取Bot Token
3. 无需额外设置，直接进行第三步

#### 方式B：使用用户会话数据

在本地运行以下命令生成会话数据：

```bash
# 安装依赖
pip install telethon

# 运行会话生成器
python generate_telegram_session.py
```

按照提示输入：
- 账户名称 (例如: account1, account2)
- API ID 和 API Hash
- 手机号码和验证码

脚本会输出base64编码的会话数据，请保存这些数据。

### 第三步：配置GitHub Secrets

在您的GitHub仓库中，进入 `Settings` -> `Secrets and variables` -> `Actions`，添加以下Secrets：

#### 必需的Secrets：

**基础配置（所有账户）:**
- `TELEGRAM_API_ID_1`: 第一个账户的API ID
- `TELEGRAM_API_HASH_1`: 第一个账户的API Hash
- `TELEGRAM_API_ID_2`: 第二个账户的API ID
- `TELEGRAM_API_HASH_2`: 第二个账户的API Hash

**认证配置（选择其一）:**

**方式A - Bot Token模式（推荐）:**
- `TELEGRAM_BOT_TOKEN1`: 第一个账户的Bot Token
- `TELEGRAM_BOT_TOKEN2`: 第二个账户的Bot Token

**方式B - 用户会话模式:**
- `TELEGRAM_SESSION_1`: 第一个账户的会话数据
- `TELEGRAM_SESSION_2`: 第二个账户的会话数据

#### 可选的Secrets：

- `NOPECHA_API_KEY`: NopeCHA API密钥（用于自动解决验证码）

### 第四步：启用GitHub Actions

1. 确保您的仓库中有 `.github/workflows/multi_vps_renew.yml` 文件
2. GitHub Actions会自动每2天运行一次
3. 您也可以在 `Actions` 标签页手动触发运行

## 工作流程说明

1. **触发**: 每2天自动运行，或手动触发
2. **环境准备**: 安装Python依赖、Chrome浏览器等
3. **配置生成**: 从GitHub Secrets生成配置文件
4. **执行续期**: 
   - 连接Telegram客户端
   - 打开Woiden续期页面
   - 自动填写表单和验证码
   - 等待Telegram验证码并自动提交
5. **结果保存**: 保存截图和日志作为artifacts

## 故障排除

### 常见错误

1. **"EOF when reading a line"**
   - 原因：在GitHub Actions环境中缺少会话数据
   - 解决：确保正确设置了 `TELEGRAM_SESSION_X` secrets

2. **"API ID or Hash cannot be empty"**
   - 原因：缺少API凭据
   - 解决：检查 `TELEGRAM_API_ID_X` 和 `TELEGRAM_API_HASH_X` secrets

3. **"Telegram客户端连接失败"**
   - 原因：会话数据无效或过期
   - 解决：重新运行 `generate_telegram_session.py` 生成新的会话数据

### 查看日志

1. 进入GitHub仓库的 `Actions` 标签页
2. 点击最近的运行记录
3. 查看详细日志
4. 下载 `screenshots-and-logs` artifact 查看截图

## 本地测试

如果需要在本地测试：

```bash
# 安装依赖
pip install -r requirements.txt

# 生成配置文件（手动编辑config.yaml）
cp config.yaml.example config.yaml

# 运行脚本
python multi_account_renewer.py
```

## 安全注意事项

1. **保护敏感信息**: 永远不要在代码中硬编码API密钥或会话数据
2. **定期更新**: 定期检查和更新会话数据
3. **监控日志**: 定期检查运行日志，确保一切正常

## 支持的功能

- ✅ 多账户支持
- ✅ 自动验证码识别
- ✅ GitHub Actions集成
- ✅ 截图和日志记录
- ✅ 错误重试机制
- ✅ 非交互式运行

## 限制

- 每个账户需要单独的Telegram API凭据
- 需要预先生成会话数据
- 依赖于Woiden网站结构（如果网站更新可能需要调整）

## 获取帮助

如果遇到问题，请：

1. 检查GitHub Actions日志
2. 查看本指南的故障排除部分
3. 在GitHub仓库中创建Issue
