<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.61">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <link rel="icon" href="/docs/images/c192.png"><title>站点基础配置(四) | 安知鱼主题指南</title><meta name="description" content="安知鱼主题站点基础配置四">
    <link rel="preload" href="/anzhiyu-docs/assets/style-47b50212.css" as="style"><link rel="stylesheet" href="/anzhiyu-docs/assets/style-47b50212.css">
    <link rel="modulepreload" href="/anzhiyu-docs/assets/app-18744df2.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/framework-2fd1fcd7.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/site-configuration4.html-e8a4bf32.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/site-configuration4.html-919542bb.js"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-e7463a10.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/page-configuration.html-cc2f4bd1.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/quick-start.html-ad03cacb.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-0505f6c4.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration1.html-50f88905.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-d3638ef1.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-7a1b5d87.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-f9875e7b.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-f69e6992.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/page-configuration.html-89a1d5ab.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/quick-start.html-5dcde832.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-2a772ecf.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration1.html-f7afdcb5.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-e0d0931a.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-74290865.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-bbf580d2.js" as="script">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container"><!--[--><header class="navbar"><div class="toggle-sidebar-button" title="toggle sidebar" aria-expanded="false" role="button" tabindex="0"><div class="icon" aria-hidden="true"><span></span><span></span><span></span></div></div><span><a href="/anzhiyu-docs/" class=""><img class="logo" src="/anzhiyu-docs/./images/c192.png" alt="安知鱼主题指南"><span class="site-name can-hide">安知鱼主题指南</span></a></span><div class="navbar-items-wrapper" style=""><!--[--><!--]--><nav class="navbar-items can-hide"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><button class="toggle-color-mode-button" title="toggle color mode"><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button><form class="search-box" role="search"><input type="search" placeholder="Search" autocomplete="off" spellcheck="false" value><!----></form></div></header><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar"><nav class="navbar-items"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><ul class="sidebar-items"><!--[--><li><a href="/anzhiyu-docs/" class="sidebar-item sidebar-heading" aria-label="简介"><!--[--><!--]--> 简介 <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/quick-start.html" class="sidebar-item sidebar-heading" aria-label="快速上手"><!--[--><!--]--> 快速上手 <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/page-configuration.html" class="sidebar-item sidebar-heading" aria-label="页面配置"><!--[--><!--]--> 页面配置 <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration1.html" class="sidebar-item sidebar-heading" aria-label="站点配置(一)"><!--[--><!--]--> 站点配置(一) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration2.html" class="sidebar-item sidebar-heading" aria-label="站点配置(二)"><!--[--><!--]--> 站点配置(二) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration3.html" class="sidebar-item sidebar-heading" aria-label="站点配置(三)"><!--[--><!--]--> 站点配置(三) <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration4.html" class="router-link-active router-link-exact-active router-link-active sidebar-item sidebar-heading active" aria-label="站点配置(四)"><!--[--><!--]--> 站点配置(四) <!--[--><!--]--></a><ul style="" class="sidebar-item-children"><!--[--><li><a aria-current="page" href="/anzhiyu-docs/site-configuration4.html#数学公式" class="router-link-active router-link-exact-active sidebar-item" aria-label="数学公式"><!--[--><!--]--> 数学公式 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration4.html#搜索系统" class="router-link-active router-link-exact-active sidebar-item" aria-label="搜索系统"><!--[--><!--]--> 搜索系统 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration4.html#分享" class="router-link-active router-link-exact-active sidebar-item" aria-label="分享"><!--[--><!--]--> 分享 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration4.html#评论" class="router-link-active router-link-exact-active sidebar-item" aria-label="评论"><!--[--><!--]--> 评论 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration4.html#在线聊天" class="router-link-active router-link-exact-active sidebar-item" aria-label="在线聊天"><!--[--><!--]--> 在线聊天 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/anzhiyu-docs/reward-list.html" class="sidebar-item sidebar-heading" aria-label="赞赏名单"><!--[--><!--]--> 赞赏名单 <!--[--><!--]--></a><!----></li><!--]--></ul><!--[--><!--]--></aside><!--]--><!--[--><main class="page"><!--[--><!--]--><div class="theme-default-content"><!--[--><!--]--><div><div class="hint-container warning"><p class="hint-container-title">警告</p><p>本教程更新于 2023 年 7 月 5 日，教程的内容针对最新的 anzhiyu 主题(如果你是旧版本，教程会有出入，请留意) 🐟 安知鱼 已经更新到 <a href="https://github.com/anzhiyu-c/hexo-theme-anzhiyu/releases/tag/1.4.0" target="_blank" rel="noopener noreferrer">1.4.0<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><h2 id="数学公式" tabindex="-1"><a class="header-anchor" href="#数学公式" aria-hidden="true">#</a> 数学公式</h2><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-8-0" aria-selected="true">Mathjax</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-8-1" aria-selected="false">KaTeX</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-8-2" aria-selected="false">hexo-renderer-markdown-it</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-8-3" aria-selected="false">hexo-renderer-markdown-it-plus</button></div><!--[--><div class="tab-item active" id="tab-8-0" role="tabpanel" aria-expanded="true"><p>不要在标题里使用 mathjax 语法，toc 目录不一定能正确显示 mathjax，可能显示 mathjax 代码</p><p>建议使用 KaTex 获得更好的效果，下文有介绍！</p><p>修改 <code>主题配置文件</code>:</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">mathjax</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token comment"># true 表示每一页都加载mathjax.js</span>
  <span class="token comment"># false 需要时加载，须在使用的Markdown Front-matter 加上 mathjax: true</span>
  <span class="token key atrule">per_page</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><blockquote><p>如果 <code>per_page</code> 设为 <code>true</code>,则每一页都会加载 Mathjax 服务。设为 <code>false</code>，则需要在文章 <code>Front-matter</code> 添加 <code>mathjax: true</code>,对应的文章才会加载 Mathjax 服务。</p></blockquote><p>然后你需要修改一下默认的 <code>markdown</code> 渲染引擎来实现 MathJax 的效果。</p><p>查看: <a href="https://www.npmjs.com/package/hexo-renderer-kramed" target="_blank" rel="noopener noreferrer">hexo-renderer-kramed<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><p>以下操作在你 hexo 博客的目录下 (<strong>不是 Anzhiyu 的目录</strong>):</p><ol><li>安装插件</li></ol><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">npm</span> uninstall hexo-renderer-marked <span class="token parameter variable">--save</span>
<span class="token function">npm</span> <span class="token function">install</span> hexo-renderer-kramed <span class="token parameter variable">--save</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div></div></div><ol start="2"><li>配置 hexo 根目录的配置文件</li></ol><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>kramed:
  gfm: <span class="token boolean">true</span>
  pedantic: <span class="token boolean">false</span>
  sanitize: <span class="token boolean">false</span>
  tables: <span class="token boolean">true</span>
  breaks: <span class="token boolean">true</span>
  smartLists: <span class="token boolean">true</span>
  smartypants: <span class="token boolean">true</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></div><div class="tab-item" id="tab-8-1" role="tabpanel" aria-expanded="false"><p>不要在标题里使用 KaTeX 语法，toc 目录不能正确显示 KaTeX。</p><p>首先禁用 <code>MathJax</code>（如果你配置过 MathJax 的话），然后修改你的<code>主题配置文件</code>以便加载 <code>katex.min.css</code>:</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">katex</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token comment"># true 表示每一页都加载katex.js</span>
  <span class="token comment"># false 需要时加载，须在使用的Markdown Front-matter 加上 katex: true</span>
  <span class="token key atrule">per_page</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">hide_scrollbar</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>你不需要添加 <code>katex.min.js</code> 来渲染数学方程。相应的你需要卸载你之前的 hexo 的 markdown 渲染器，然后安装其它插件。</p><p>因为 KaTeX 更快更轻量，因此没有 MathJax 的功能多（比如右键菜单）。为那些使用 MathJax 的用户，主题也内置了 katex 的 <a href="https://github.com/KaTeX/KaTeX/tree/master/contrib/copy-tex" target="_blank" rel="noopener noreferrer">复制<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 功能。</p></div><div class="tab-item" id="tab-8-2" role="tabpanel" aria-expanded="false"><p>卸载掉 marked 插件，安装 <a href="https://github.com/hexojs/hexo-renderer-markdown-it" target="_blank" rel="noopener noreferrer">hexo-renderer-markdown-it<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><div class="language-BASH line-numbers-mode" data-ext="BASH"><pre class="language-BASH"><code>npm un hexo-renderer-marked --save # 如果有安装这个的话，卸载
npm un hexo-renderer-kramed --save # 如果有安装这个的话，卸载

npm i hexo-renderer-markdown-it --save # 需要安装这个渲染插件
npm install katex @renbaoshuo/markdown-it-katex #需要安装这个katex插件

</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>在 hexo 的根目录的<code>_config.yml</code> 中配置</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">markdown</span><span class="token punctuation">:</span>
  <span class="token key atrule">plugins</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token string">&quot;@renbaoshuo/markdown-it-katex&quot;</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>如需配置其它参数，请参考 <a href="https://katex.org/docs/options.html" target="_blank" rel="noopener noreferrer">katex 官网<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><div class="tab-item" id="tab-8-3" role="tabpanel" aria-expanded="false"><blockquote><p>注意，此方法生成的 katex 没有斜体</p></blockquote><p>卸载掉 marked 插件，然后安装新的<code>hexo-renderer-markdown-it-plus</code>:</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token comment"># 替换 `hexo-renderer-kramed` 或者 `hexo-renderer-marked` 等hexo的markdown渲染器</span>
<span class="token comment"># 你可以在你的package.json里找到hexo的markdwon渲染器，并将其卸载</span>
<span class="token function">npm</span> un hexo-renderer-marked <span class="token parameter variable">--save</span>

<span class="token comment"># or</span>

<span class="token function">npm</span> un hexo-renderer-kramed <span class="token parameter variable">--save</span>


<span class="token comment"># 然后安装 `hexo-renderer-markdown-it-plus`</span>
<span class="token function">npm</span> i @upupming/hexo-renderer-markdown-it-plus <span class="token parameter variable">--save</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>注意到 <code>hexo-renderer-markdown-it-plus</code> 已经无人持续维护, 所以我们使用 <code>@upupming/hexo-renderer-markdown-it-plus</code>。 这份 fork 的代码使用了 <code>@neilsustc/markdown-it-katex</code> 同时它也是 VSCode 的插件 <a href="https://github.com/yzhang-gh/vscode-markdown" target="_blank" rel="noopener noreferrer">Markdown All in One<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 所使用的, 所以我们可以获得最新的 KaTex 功能例如 \tag{}。</p><p>你还可以通过 <code>@neilsustc/markdown-it-katex</code> 控制 KaTeX 的设置，所有可配置的选项参见 <a href="https://katex.org/docs/options.html" target="_blank" rel="noopener noreferrer">https://katex.org/docs/options.html<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>。 比如你想要禁用掉 KaTeX 在命令行上输出的宂长的警告信息，你可以在根目录的 _config.yml 中使用下面的配置将 strict 设置为 false</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>markdown_it_plus:
  plugins:
    - plugin:
      name: <span class="token string">&#39;@neilsustc/markdown-it-katex&#39;</span>
      enable: <span class="token boolean">true</span>
      options:
        strict: <span class="token boolean">false</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>当然，你还可以利用这个特性来定义一些自己常用的 <code>macros</code>。</p></div><!--]--></div><h2 id="搜索系统" tabindex="-1"><a class="header-anchor" href="#搜索系统" aria-hidden="true">#</a> 搜索系统</h2><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-104-0" aria-selected="true">algolia</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-104-1" aria-selected="false">本地搜索</button></div><!--[--><div class="tab-item active" id="tab-104-0" role="tabpanel" aria-expanded="true"><p>记得运行 hexo clean</p><p>使用 <a href="https://github.com/LouisBarranqueiro/hexo-algoliasearch" target="_blank" rel="noopener noreferrer">hexo-algoliasearch<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>，请记得配置 fields 参数的 <code>title</code>, <code>permalink</code> 和 <code>content</code></p><p>你需要安装 <a href="https://github.com/LouisBarranqueiro/hexo-algoliasearch" target="_blank" rel="noopener noreferrer">hexo-algoliasearch<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>. 根据它的说明文档去做相应的配置。</p><p>修改 主题配置文件</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">algolia_search</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">hits</span><span class="token punctuation">:</span>
    <span class="token key atrule">per_page</span><span class="token punctuation">:</span> <span class="token number">6</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>hexo 配置文件</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># algolia搜索: https://github.com/LouisBarranqueiro/hexo-algoliasearch</span>
<span class="token key atrule">algolia</span><span class="token punctuation">:</span>
  <span class="token key atrule">appId</span><span class="token punctuation">:</span> <span class="token string">&quot;xxxx&quot;</span>
  <span class="token key atrule">apiKey</span><span class="token punctuation">:</span> <span class="token string">&quot;xxxx&quot;</span>
  <span class="token key atrule">adminApiKey</span><span class="token punctuation">:</span> <span class="token string">&quot;xxxx&quot;</span>
  <span class="token key atrule">chunkSize</span><span class="token punctuation">:</span> <span class="token number">5000</span>
  <span class="token key atrule">indexName</span><span class="token punctuation">:</span> <span class="token string">&quot;hexo&quot;</span>
  <span class="token key atrule">fields</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> content<span class="token punctuation">:</span>strip<span class="token punctuation">:</span>truncate<span class="token punctuation">,</span><span class="token number">0</span><span class="token punctuation">,</span><span class="token number">200</span>
    <span class="token punctuation">-</span> excerpt<span class="token punctuation">:</span>strip
    <span class="token punctuation">-</span> gallery
    <span class="token punctuation">-</span> permalink
    <span class="token punctuation">-</span> photos
    <span class="token punctuation">-</span> slug
    <span class="token punctuation">-</span> tags
    <span class="token punctuation">-</span> title
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></div><div class="tab-item" id="tab-104-1" role="tabpanel" aria-expanded="false"><p>记得运行 <code>hexo clean</code></p><p>你需要安装 <a href="https://github.com/wzpan/hexo-generator-search" target="_blank" rel="noopener noreferrer">hexo-generator-search<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>，根据它的文档去做相应配置</p><p>修改 主题配置文件</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">local_search</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">preload</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">CDN</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>解释</th></tr></thead><tbody><tr><td>enable</td><td>是否开启本地搜索</td></tr><tr><td>preload</td><td>预加载，开启后，进入网页后会自动加载搜索文件。关闭时，只有点击搜索按钮后，才会加载搜索文件</td></tr><tr><td>CDN</td><td>搜索文件的 CDN 地址（默认使用的本地链接）</td></tr></tbody></table></div><!--]--></div><h2 id="分享" tabindex="-1"><a class="header-anchor" href="#分享" aria-hidden="true">#</a> 分享</h2><p>只能选择一个分享服务商</p><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-181-0" aria-selected="true">AddThis</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-181-1" aria-selected="false">sharejs</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-181-2" aria-selected="false">addtoany</button></div><!--[--><div class="tab-item active" id="tab-181-0" role="tabpanel" aria-expanded="true"><p>访问 <a href="https://www.addthis.com/" target="_blank" rel="noopener noreferrer">AddThis<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 官网 找到你的 pub-id</p><figure><img src="https://file.crazywong.com/gh/jerryc127/CDN/img/hexo-theme-butterfly-doc-addthis.jpg" alt tabindex="0" loading="lazy"><figcaption></figcaption></figure><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">addThis</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># or false</span>
  <span class="token key atrule">pubid</span><span class="token punctuation">:</span> 你的pub<span class="token punctuation">-</span>id
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></div><div class="tab-item" id="tab-181-1" role="tabpanel" aria-expanded="false"><p>如果你不知道 <a href="https://github.com/overtrue/share.js/" target="_blank" rel="noopener noreferrer">sharejs<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>，看看它的説明。</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">sharejs</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">sites</span><span class="token punctuation">:</span> facebook<span class="token punctuation">,</span>twitter<span class="token punctuation">,</span>wechat<span class="token punctuation">,</span>weibo<span class="token punctuation">,</span>qq <span class="token comment">#想要显示的内容</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></div><div class="tab-item" id="tab-181-2" role="tabpanel" aria-expanded="false"><p>可以到 <a href="https://www.addtoany.com/" target="_blank" rel="noopener noreferrer">addtoany<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 查看使用説明</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">addtoany</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">item</span><span class="token punctuation">:</span> facebook<span class="token punctuation">,</span>twitter<span class="token punctuation">,</span>wechat<span class="token punctuation">,</span>sina_weibo<span class="token punctuation">,</span>facebook_messenger<span class="token punctuation">,</span>email<span class="token punctuation">,</span>copy_link
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></div><!--]--></div><h2 id="评论" tabindex="-1"><a class="header-anchor" href="#评论" aria-hidden="true">#</a> 评论</h2><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-213-0" aria-selected="true">通用配置</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-213-1" aria-selected="false">Twikoo</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-213-2" aria-selected="false">Valine</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-213-3" aria-selected="false">Waline</button></div><!--[--><div class="tab-item active" id="tab-213-0" role="tabpanel" aria-expanded="true"><p>开启评论需要在 comments-use 中填写你需要的评论。</p><p>支持双评论显示，只需要配置两个评论（第一个为默认显示）</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">comments</span><span class="token punctuation">:</span>
  <span class="token comment"># Up to two comments system, the first will be shown as default</span>
  <span class="token comment"># Choose: Valine/Waline/Twikoo/</span>
  <span class="token key atrule">use</span><span class="token punctuation">:</span> Twikoo<span class="token punctuation">,</span>Waline <span class="token comment"># Twikoo/Waline</span>
  <span class="token key atrule">text</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># Display the comment name next to the button</span>
  <span class="token comment"># lazyload: The comment system will be load when comment element enters the browser&#39;s viewport.</span>
  <span class="token comment"># If you set it to true, the comment count will be invalid</span>
  <span class="token key atrule">lazyload</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">count</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># Display comment count in post&#39;s top_img</span>
  <span class="token key atrule">card_post_count</span><span class="token punctuation">:</span> <span class="token boolean important">false</span> <span class="token comment"># Display comment count in Home Page</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>解释</th></tr></thead><tbody><tr><td>use</td><td>使用的评论（请注意，最多支持两个，如果不需要请留空）</td></tr><tr><td>text</td><td>是否显示评论服务商的名字</td></tr><tr><td>lazyload</td><td>是否为评论开启 lazyload，开启后，只有滚动到评论位置时才会加载评论所需要的资源（开启 lazyload 后，评论数将不显示）</td></tr><tr><td>count</td><td>是否在文章顶部显示评论数</td></tr><tr><td>card_post_count</td><td>是否在首页文章卡片显示评论数</td></tr></tbody></table></div><div class="tab-item" id="tab-213-1" role="tabpanel" aria-expanded="false"><p><code>Twikoo</code> 是一个简洁、安全、无后端的静态网站评论系统，基于<a href="https://cloud.tencent.com/product/tcb" target="_blank" rel="noopener noreferrer">腾讯云开发<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>。</p><p>具体如何配置评论，请查看 <a href="https://twikoo.js.org/quick-start.html" target="_blank" rel="noopener noreferrer">Twikoo 文档<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><p>你只需要把获取到的 <code>环境 ID (envId)</code> 填写到配置上去就行</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># Twikoo</span>
<span class="token comment"># https://github.com/imaegoo/twikoo</span>
<span class="token key atrule">twikoo</span><span class="token punctuation">:</span>
  <span class="token key atrule">envId</span><span class="token punctuation">:</span>
  <span class="token key atrule">region</span><span class="token punctuation">:</span>
  <span class="token key atrule">visitor</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">option</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>解释</th></tr></thead><tbody><tr><td>envId</td><td>环境 ID</td></tr><tr><td>region</td><td>环境地域，默认为 ap-shanghai，如果您的环境地域不是上海，需传此参数</td></tr><tr><td>visitor</td><td>是否显示文章阅读数</td></tr><tr><td>option</td><td>可选配置</td></tr><tr><td>card_post_count</td><td>是否在首页文章卡片显示评论数</td></tr></tbody></table><p>开启 visitor 后，文章页的访问人数将改为 Twikoo 提供，而不是 <code>不蒜子</code></p></div><div class="tab-item" id="tab-213-2" role="tabpanel" aria-expanded="false"><p>遵循 <a href="https://github.com/xCss/Valine" target="_blank" rel="noopener noreferrer">Valine<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 的指示去配置你的 LeanCloud 应用。以及查看相应的配置说明。</p><p>然后修改 <code>主题配置文件</code>:</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">valine</span><span class="token punctuation">:</span>
  <span class="token key atrule">appId</span><span class="token punctuation">:</span> <span class="token comment"># leancloud application app id</span>
  <span class="token key atrule">appKey</span><span class="token punctuation">:</span> <span class="token comment"># leancloud application app key</span>
  <span class="token key atrule">avatar</span><span class="token punctuation">:</span> monsterid <span class="token comment"># gravatar style https://valine.js.org/#/avatar</span>
  <span class="token key atrule">serverURLs</span><span class="token punctuation">:</span> <span class="token comment"># This configuration is suitable for domestic custom domain name users, overseas version will be automatically detected (no need to manually fill in)</span>
  <span class="token key atrule">bg</span><span class="token punctuation">:</span> <span class="token comment"># valine background</span>
  <span class="token key atrule">visitor</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">option</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>开启 visitor 后，文章页的访问人数将改为 Valine 提供，而不是 不蒜子</p><p>Valine 于 v1.4.5 开始支持自定义表情，如果你需要自行配置，请在 emojiCDN 配置表情 CDN。</p><p>同时在 Hexo 工作目录下的 source/_data/创建一个 json 文件 valine.json,等同于 Valine 需要配置的 emojiMaps，valine.json 配置方式可参考如下</p><p>valine.json</p><div class="language-json line-numbers-mode" data-ext="json"><pre class="language-json"><code><span class="token punctuation">{</span>
  <span class="token property">&quot;tv_doge&quot;</span><span class="token operator">:</span> <span class="token string">&quot;6ea59c827c414b4a2955fe79e0f6fd3dcd515e24.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_亲亲&quot;</span><span class="token operator">:</span> <span class="token string">&quot;a8111ad55953ef5e3be3327ef94eb4a39d535d06.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_偷笑&quot;</span><span class="token operator">:</span> <span class="token string">&quot;bb690d4107620f1c15cff29509db529a73aee261.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_再见&quot;</span><span class="token operator">:</span> <span class="token string">&quot;180129b8ea851044ce71caf55cc8ce44bd4a4fc8.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_冷漠&quot;</span><span class="token operator">:</span> <span class="token string">&quot;b9cbc755c2b3ee43be07ca13de84e5b699a3f101.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_发怒&quot;</span><span class="token operator">:</span> <span class="token string">&quot;34ba3cd204d5b05fec70ce08fa9fa0dd612409ff.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_发财&quot;</span><span class="token operator">:</span> <span class="token string">&quot;34db290afd2963723c6eb3c4560667db7253a21a.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_可爱&quot;</span><span class="token operator">:</span> <span class="token string">&quot;9e55fd9b500ac4b96613539f1ce2f9499e314ed9.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_吐血&quot;</span><span class="token operator">:</span> <span class="token string">&quot;09dd16a7aa59b77baa1155d47484409624470c77.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_呆&quot;</span><span class="token operator">:</span> <span class="token string">&quot;fe1179ebaa191569b0d31cecafe7a2cd1c951c9d.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_呕吐&quot;</span><span class="token operator">:</span> <span class="token string">&quot;9f996894a39e282ccf5e66856af49483f81870f3.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_困&quot;</span><span class="token operator">:</span> <span class="token string">&quot;241ee304e44c0af029adceb294399391e4737ef2.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_坏笑&quot;</span><span class="token operator">:</span> <span class="token string">&quot;1f0b87f731a671079842116e0991c91c2c88645a.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_大佬&quot;</span><span class="token operator">:</span> <span class="token string">&quot;093c1e2c490161aca397afc45573c877cdead616.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_大哭&quot;</span><span class="token operator">:</span> <span class="token string">&quot;23269aeb35f99daee28dda129676f6e9ea87934f.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_委屈&quot;</span><span class="token operator">:</span> <span class="token string">&quot;d04dba7b5465779e9755d2ab6f0a897b9b33bb77.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_害羞&quot;</span><span class="token operator">:</span> <span class="token string">&quot;a37683fb5642fa3ddfc7f4e5525fd13e42a2bdb1.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_尴尬&quot;</span><span class="token operator">:</span> <span class="token string">&quot;7cfa62dafc59798a3d3fb262d421eeeff166cfa4.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_微笑&quot;</span><span class="token operator">:</span> <span class="token string">&quot;70dc5c7b56f93eb61bddba11e28fb1d18fddcd4c.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_思考&quot;</span><span class="token operator">:</span> <span class="token string">&quot;90cf159733e558137ed20aa04d09964436f618a1.png&quot;</span><span class="token punctuation">,</span>
  <span class="token property">&quot;tv_惊吓&quot;</span><span class="token operator">:</span> <span class="token string">&quot;0d15c7e2ee58e935adc6a7193ee042388adc22af.png&quot;</span>
<span class="token punctuation">}</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></div><div class="tab-item" id="tab-213-3" role="tabpanel" aria-expanded="false"><p>Waline - 一款从 Valine 衍生的带后端评论系统。可以将 Waline 等价成 With backend Valine。</p><p>具体配置可参考 <a href="https://waline.js.org/" target="_blank" rel="noopener noreferrer">waline 文档<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><p>然后修改 主题配置文件:</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">waline</span><span class="token punctuation">:</span>
  <span class="token key atrule">serverURL</span><span class="token punctuation">:</span> <span class="token comment"># Waline server address url</span>
  <span class="token key atrule">bg</span><span class="token punctuation">:</span> <span class="token comment"># waline background</span>
  <span class="token key atrule">pageview</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">option</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>开启 pageview 后，文章页的访问人数将改为 Waline 提供，而不是 不蒜子</p></div><!--]--></div><h2 id="在线聊天" tabindex="-1"><a class="header-anchor" href="#在线聊天" aria-hidden="true">#</a> 在线聊天</h2><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-390-0" aria-selected="true">通用配置</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-390-1" aria-selected="false">chatra</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-390-2" aria-selected="false">tidio</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-390-3" aria-selected="false">daovoice</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-390-4" aria-selected="false">crisp</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-390-5" aria-selected="false">messenger</button></div><!--[--><div class="tab-item active" id="tab-390-0" role="tabpanel" aria-expanded="true"><p>这些工具都提供了一个按钮可以打开/关闭聊天窗口。 主题也提供了一个集合主题特色的按钮来替换这些工具本身的按钮，这个聊天按钮将会出现在右下角里。 你只需要把 <code>chat_btn</code> 打开就行。</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># Chat Button [recommend]</span>
<span class="token comment"># It will create a button in the bottom right corner of website, and hide the origin button</span>
<span class="token key atrule">chat_btn</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>为了不影响访客的体验，主题提供一个 <code>chat_hide_show</code> 配置 设为 true 后，使用工具提供的按钮时，只有向上滚动才会显示聊天按钮，向下滚动时会隐藏按钮。</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># The origin chat button is displayed when scrolling up, and the button is hidden when scrolling down</span>
<span class="token key atrule">chat_hide_show</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div></div></div><p>如果使用工具自带的聊天按钮，按钮位置可能会遮挡右下角图标，请配置 <code>rightside-bottom</code> 调正右下角图标位置</p></div><div class="tab-item" id="tab-390-1" role="tabpanel" aria-expanded="false"><p>配置 <a href="https://chatra.com/cn/" target="_blank" rel="noopener noreferrer">chatra<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a>,需要知道 <code>Public key</code></p><p>打开 <a href="https://chatra.com/cn/" target="_blank" rel="noopener noreferrer">chatra<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 并注册账号。 你可以在 <code>Preferences</code> 中找到 <code>Public key</code></p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># chatra</span>
<span class="token comment"># https://chatra.io/</span>
<span class="token key atrule">chatra</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">id</span><span class="token punctuation">:</span> xxxxxxxx
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><code>chatra</code> 的样式你可以 <code>Chat Widget</code> 自行配置</p></div><div class="tab-item" id="tab-390-2" role="tabpanel" aria-expanded="false"><p>配置 tidio,需要知道 <code>Public key</code></p><p>打开 <a href="https://www.tidio.com/" target="_blank" rel="noopener noreferrer">tidio<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 并注册账号。 你可以在 <code>Preferences &gt; Developer</code> 中找到 <code>Public key</code></p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># tidio</span>
<span class="token comment"># https://www.tidio.com/</span>
<span class="token key atrule">tidio</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">public_key</span><span class="token punctuation">:</span> XXXX
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><code>tidio</code>的样式你可以<code>Channels</code>自行配置</p></div><div class="tab-item" id="tab-390-3" role="tabpanel" aria-expanded="false"><p>打开 <a href="https://www.daocloud.io/" target="_blank" rel="noopener noreferrer">daovoice<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 和注册帐号</p><p>找到你的 <code>app id</code><img src="https://file.crazywong.com/gh/jerryc127/CDN/img/hexo-theme-butterfly-docs-chat-daovoice-appid.png" alt loading="lazy"></p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># daovoice</span>
<span class="token comment"># http://daovoice.io/</span>
<span class="token key atrule">daovoice</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">app_id</span><span class="token punctuation">:</span> xxxxx
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>可在<code>聊天设置</code>里配置聊天按钮等样式</p></div><div class="tab-item" id="tab-390-4" role="tabpanel" aria-expanded="false"><p>打开 <a href="https://crisp.chat/en/" target="_blank" rel="noopener noreferrer">crisp<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 并注册帐号</p><p>找到需要的网站 ID</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># crisp</span>
<span class="token comment"># https://crisp.chat/en/</span>
<span class="token key atrule">crisp</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">website_id</span><span class="token punctuation">:</span> xxxxxxxx
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://file.crazywong.com/gh/jerryc127/CDN/img/hexo-theme-buttefly-docs-chat-crisp.png" alt tabindex="0" loading="lazy"><figcaption></figcaption></figure></div><div class="tab-item" id="tab-390-5" role="tabpanel" aria-expanded="false"><p>messenger 为 Facebook 旗下的聊天服务</p><p>具体操作请查看 <a href="https://developers.facebook.com/docs/messenger-platform/discovery/facebook-chat-plugin/" target="_blank" rel="noopener noreferrer">Facebook 洽谈附加程式 - Messenger 平台<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">messenger</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">pageID</span><span class="token punctuation">:</span> xxxxx
  <span class="token key atrule">lang</span><span class="token punctuation">:</span> zh_TW <span class="token comment"># Language en_US/zh_CN/zh_TW and so on</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div></div><!--]--></div></div><!--[--><!--]--></div><footer class="page-meta"><!----><div class="meta-item last-updated"><span class="meta-item-label">上次更新: </span><!----></div><div class="meta-item contributors"><span class="meta-item-label">贡献者: </span><span class="meta-item-info"><!--[--><!--[--><span class="contributor" title="email: <EMAIL>">anzhiyu</span><!----><!--]--><!--]--></span></div></footer><nav class="page-nav"><p class="inner"><span class="prev"><a href="/anzhiyu-docs/site-configuration3.html" class="" aria-label="站点配置(三)"><!--[--><!--]--> 站点配置(三) <!--[--><!--]--></a></span><span class="next"><a href="/anzhiyu-docs/reward-list.html" class="" aria-label="赞赏名单"><!--[--><!--]--> 赞赏名单 <!--[--><!--]--></a></span></p></nav><!--[--><!--]--></main><!--]--></div><!----><!--]--></div>
    <script type="module" src="/anzhiyu-docs/assets/app-18744df2.js" defer></script>
  </body>
</html>
