const e=JSON.parse('{"key":"v-8daa1a0e","path":"/","title":"","lang":"zh-CN","frontmatter":{},"headers":[{"level":2,"title":"预览","slug":"预览","link":"#预览","children":[]},{"level":2,"title":"特色","slug":"特色","link":"#特色","children":[]},{"level":2,"title":"交流群","slug":"交流群","link":"#交流群","children":[]},{"level":2,"title":"历程","slug":"历程","link":"#历程","children":[]}],"git":{"updatedTime":1686136773000,"contributors":[{"name":"anzhiyu","email":"<EMAIL>","commits":3},{"name":"<PERSON><PERSON>ell<PERSON>","email":"<EMAIL>","commits":2},{"name":"GH Action - Upstream Sync","email":"<EMAIL>","commits":1},{"name":"name","email":"<EMAIL>","commits":1}]},"filePathRelative":"README.md"}');export{e as data};
