import{d as ae,r as x,a as je,b as qn,c as A,i as te,e as Ve,f as Wn,g as Jt,o as J,h as g,j as S,k as ce,l as $e,m as Qt,u as he,n as nt,T as ot,p as h,q as y,s as D,t as ke,v as V,w as ne,x as Yt,_ as M,y as Un,z as p,A as oe,B as rt,C as Oe,D as Zt,E as Xt,F as en,G as Kn,H as at,I as tn,J as Gn,K as lt,L as ge,M as ue,N as H,O as X,P as pe,Q as B,R as z,S as qe,U,V as G,W as _t,X as Jn,Y as Qn,Z as De,$ as He,a0 as nn,a1 as Yn,a2 as Zn,a3 as Xn,a4 as eo,a5 as to,a6 as no,a7 as oo}from"./framework-2fd1fcd7.js";const ro="modulepreload",ao=function(e){return"/anzhiyu-docs/"+e},bt={},W=function(t,n,o){if(!n||n.length===0)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map(a=>{if(a=ao(a),a in bt)return;bt[a]=!0;const c=a.endsWith(".css"),l=c?'[rel="stylesheet"]':"";if(!!o)for(let u=r.length-1;u>=0;u--){const v=r[u];if(v.href===a&&(!c||v.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${a}"]${l}`))return;const s=document.createElement("link");if(s.rel=c?"stylesheet":ro,c||(s.as="script",s.crossOrigin=""),s.href=a,document.head.appendChild(s),c)return new Promise((u,v)=>{s.addEventListener("load",u),s.addEventListener("error",()=>v(new Error(`Unable to preload CSS for ${a}`)))})})).then(()=>t())},lo={"v-8daa1a0e":()=>W(()=>import("./index.html-e7463a10.js"),[]).then(({data:e})=>e),"v-65358688":()=>W(()=>import("./page-configuration.html-cc2f4bd1.js"),[]).then(({data:e})=>e),"v-96f5eae0":()=>W(()=>import("./quick-start.html-ad03cacb.js"),[]).then(({data:e})=>e),"v-a39f3254":()=>W(()=>import("./reward-list.html-0505f6c4.js"),[]).then(({data:e})=>e),"v-5dbf1831":()=>W(()=>import("./site-configuration1.html-50f88905.js"),[]).then(({data:e})=>e),"v-5f73f0d0":()=>W(()=>import("./site-configuration2.html-d3638ef1.js"),[]).then(({data:e})=>e),"v-6128c96f":()=>W(()=>import("./site-configuration3.html-7a1b5d87.js"),[]).then(({data:e})=>e),"v-62dda20e":()=>W(()=>import("./site-configuration4.html-e8a4bf32.js"),[]).then(({data:e})=>e),"v-3706649a":()=>W(()=>import("./404.html-f9875e7b.js"),[]).then(({data:e})=>e)},so=JSON.parse('{"base":"/anzhiyu-docs/","lang":"zh-CN","title":"安知鱼主题指南","description":"anzhiyu,hexo主题,anzhiyu主题,安知鱼,博客,魔改,简单的hexo主题,简洁的hexo主题,hexo","head":[["link",{"rel":"icon","href":"/docs/images/c192.png"}]],"locales":{}}'),on={"v-8daa1a0e":ae(()=>W(()=>import("./index.html-f69e6992.js"),["assets/index.html-f69e6992.js","assets/framework-2fd1fcd7.js"])),"v-65358688":ae(()=>W(()=>import("./page-configuration.html-89a1d5ab.js"),["assets/page-configuration.html-89a1d5ab.js","assets/framework-2fd1fcd7.js"])),"v-96f5eae0":ae(()=>W(()=>import("./quick-start.html-5dcde832.js"),["assets/quick-start.html-5dcde832.js","assets/framework-2fd1fcd7.js"])),"v-a39f3254":ae(()=>W(()=>import("./reward-list.html-2a772ecf.js"),["assets/reward-list.html-2a772ecf.js","assets/framework-2fd1fcd7.js"])),"v-5dbf1831":ae(()=>W(()=>import("./site-configuration1.html-f7afdcb5.js"),["assets/site-configuration1.html-f7afdcb5.js","assets/framework-2fd1fcd7.js"])),"v-5f73f0d0":ae(()=>W(()=>import("./site-configuration2.html-e0d0931a.js"),["assets/site-configuration2.html-e0d0931a.js","assets/framework-2fd1fcd7.js"])),"v-6128c96f":ae(()=>W(()=>import("./site-configuration3.html-74290865.js"),["assets/site-configuration3.html-74290865.js","assets/framework-2fd1fcd7.js"])),"v-62dda20e":ae(()=>W(()=>import("./site-configuration4.html-919542bb.js"),["assets/site-configuration4.html-919542bb.js","assets/framework-2fd1fcd7.js"])),"v-3706649a":ae(()=>W(()=>import("./404.html-bbf580d2.js"),["assets/404.html-bbf580d2.js","assets/framework-2fd1fcd7.js"]))};var io=Symbol(""),uo=x(lo),rn=je({key:"",path:"",title:"",lang:"",frontmatter:{},headers:[]}),ve=x(rn),fe=()=>ve,an=Symbol(""),se=()=>{const e=ce(an);if(!e)throw new Error("usePageFrontmatter() is called without provider.");return e},ln=Symbol(""),co=()=>{const e=ce(ln);if(!e)throw new Error("usePageHead() is called without provider.");return e},vo=Symbol(""),sn=Symbol(""),fo=()=>{const e=ce(sn);if(!e)throw new Error("usePageLang() is called without provider.");return e},un=Symbol(""),po=()=>{const e=ce(un);if(!e)throw new Error("usePageLayout() is called without provider.");return e},st=Symbol(""),we=()=>{const e=ce(st);if(!e)throw new Error("useRouteLocale() is called without provider.");return e},ye=x(so),mo=()=>ye,cn=Symbol(""),it=()=>{const e=ce(cn);if(!e)throw new Error("useSiteLocaleData() is called without provider.");return e},ho=Symbol(""),go="Layout",_o="NotFound",le=qn({resolveLayouts:e=>e.reduce((t,n)=>({...t,...n.layouts}),{}),resolvePageData:async e=>{const t=uo.value[e];return await(t==null?void 0:t())??rn},resolvePageFrontmatter:e=>e.frontmatter,resolvePageHead:(e,t,n)=>{const o=te(t.description)?t.description:n.description,r=[...Ve(t.head)?t.head:[],...n.head,["title",{},e],["meta",{name:"description",content:o}]];return Wn(r)},resolvePageHeadTitle:(e,t)=>[e.title,t.title].filter(n=>!!n).join(" | "),resolvePageLang:e=>e.lang||"en",resolvePageLayout:(e,t)=>{let n;if(e.path){const o=e.frontmatter.layout;te(o)?n=o:n=go}else n=_o;return t[n]},resolveRouteLocale:(e,t)=>Jt(e,t),resolveSiteLocaleData:(e,t)=>({...e,...e.locales[t]})}),ut=A({name:"ClientOnly",setup(e,t){const n=x(!1);return J(()=>{n.value=!0}),()=>{var o,r;return n.value?(r=(o=t.slots).default)==null?void 0:r.call(o):null}}}),bo=A({name:"Content",props:{pageKey:{type:String,required:!1,default:""}},setup(e){const t=fe(),n=g(()=>on[e.pageKey||t.value.key]);return()=>n.value?S(n.value):S("div","404 Not Found")}}),re=(e={})=>e,ct=e=>$e(e)?e:`/anzhiyu-docs/${Qt(e)}`;function dn(e,t,n){var o,r,a;t===void 0&&(t=50),n===void 0&&(n={});var c=(o=n.isImmediate)!=null&&o,l=(r=n.callback)!=null&&r,i=n.maxWait,s=Date.now(),u=[];function v(){if(i!==void 0){var m=Date.now()-s;if(m+t>=i)return i-m}return t}var f=function(){var m=[].slice.call(arguments),b=this;return new Promise(function(k,_){var w=c&&a===void 0;if(a!==void 0&&clearTimeout(a),a=setTimeout(function(){if(a=void 0,s=Date.now(),!c){var T=e.apply(b,m);l&&l(T),u.forEach(function(F){return(0,F.resolve)(T)}),u=[]}},v()),w){var N=e.apply(b,m);return l&&l(N),k(N)}u.push({resolve:k,reject:_})})};return f.cancel=function(m){a!==void 0&&clearTimeout(a),u.forEach(function(b){return(0,b.reject)(m)}),u=[]},f}const yo=({headerLinkSelector:e,headerAnchorSelector:t,delay:n,offset:o=5})=>{const r=he(),c=dn(()=>{var k,_;const l=Math.max(window.scrollY,document.documentElement.scrollTop,document.body.scrollTop);if(Math.abs(l-0)<o){yt(r,"");return}const s=window.innerHeight+l,u=Math.max(document.documentElement.scrollHeight,document.body.scrollHeight),v=Math.abs(u-s)<o,f=Array.from(document.querySelectorAll(e)),b=Array.from(document.querySelectorAll(t)).filter(w=>f.some(N=>N.hash===w.hash));for(let w=0;w<b.length;w++){const N=b[w],T=b[w+1],F=l>=(((k=N.parentElement)==null?void 0:k.offsetTop)??0)-o,q=!T||l<(((_=T.parentElement)==null?void 0:_.offsetTop)??0)-o;if(!(F&&q))continue;const L=decodeURIComponent(r.currentRoute.value.hash),d=decodeURIComponent(N.hash);if(L===d)return;if(v){for(let $=w+1;$<b.length;$++)if(L===decodeURIComponent(b[$].hash))return}yt(r,d);return}},n);J(()=>{window.addEventListener("scroll",c)}),nt(()=>{window.removeEventListener("scroll",c)})},yt=async(e,t)=>{const{scrollBehavior:n}=e.options;e.options.scrollBehavior=void 0,await e.replace({query:e.currentRoute.value.query,hash:t,force:!0}).finally(()=>e.options.scrollBehavior=n)},ko="a.sidebar-item",wo=".header-anchor",Lo=300,So=5,Eo=re({setup(){yo({headerLinkSelector:ko,headerAnchorSelector:wo,delay:Lo,offset:So})}}),kt=()=>window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,$o=()=>window.scrollTo({top:0,behavior:"smooth"});const Oo=A({name:"BackToTop",setup(){const e=x(0),t=g(()=>e.value>300),n=dn(()=>{e.value=kt()},100);J(()=>{e.value=kt(),window.addEventListener("scroll",()=>n())});const o=S("div",{class:"back-to-top",onClick:$o});return()=>S(ot,{name:"back-to-top"},()=>t.value?o:null)}}),To=re({rootComponents:[Oo]});const Po=S("svg",{class:"external-link-icon",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",x:"0px",y:"0px",viewBox:"0 0 100 100",width:"15",height:"15"},[S("path",{fill:"currentColor",d:"M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"}),S("polygon",{fill:"currentColor",points:"45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"})]),xo=A({name:"ExternalLinkIcon",props:{locales:{type:Object,required:!1,default:()=>({})}},setup(e){const t=we(),n=g(()=>e.locales[t.value]??{openInNewWindow:"open in new window"});return()=>S("span",[Po,S("span",{class:"external-link-icon-sr-only"},n.value.openInNewWindow)])}}),Co={"/":{openInNewWindow:"open in new window"}},Io=re({enhance({app:e}){e.component("ExternalLinkIcon",S(xo,{locales:Co}))}});/*! medium-zoom 1.0.8 | MIT License | https://github.com/francoischalifour/medium-zoom */var me=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},xe=function(t){return t.tagName==="IMG"},Ao=function(t){return NodeList.prototype.isPrototypeOf(t)},Ne=function(t){return t&&t.nodeType===1},wt=function(t){var n=t.currentSrc||t.src;return n.substr(-4).toLowerCase()===".svg"},Lt=function(t){try{return Array.isArray(t)?t.filter(xe):Ao(t)?[].slice.call(t).filter(xe):Ne(t)?[t].filter(xe):typeof t=="string"?[].slice.call(document.querySelectorAll(t)).filter(xe):[]}catch{throw new TypeError(`The provided selector is invalid.
Expects a CSS selector, a Node element, a NodeList or an array.
See: https://github.com/francoischalifour/medium-zoom`)}},zo=function(t){var n=document.createElement("div");return n.classList.add("medium-zoom-overlay"),n.style.background=t,n},No=function(t){var n=t.getBoundingClientRect(),o=n.top,r=n.left,a=n.width,c=n.height,l=t.cloneNode(),i=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,s=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0;return l.removeAttribute("id"),l.style.position="absolute",l.style.top=o+i+"px",l.style.left=r+s+"px",l.style.width=a+"px",l.style.height=c+"px",l.style.transform="",l},be=function(t,n){var o=me({bubbles:!1,cancelable:!1,detail:void 0},n);if(typeof window.CustomEvent=="function")return new CustomEvent(t,o);var r=document.createEvent("CustomEvent");return r.initCustomEvent(t,o.bubbles,o.cancelable,o.detail),r},Do=function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=window.Promise||function(E){function P(){}E(P,P)},r=function(E){var P=E.target;if(P===$){b();return}T.indexOf(P)!==-1&&k({target:P})},a=function(){if(!(q||!d.original)){var E=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(Z-E)>L.scrollOffset&&setTimeout(b,150)}},c=function(E){var P=E.key||E.keyCode;(P==="Escape"||P==="Esc"||P===27)&&b()},l=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},P=E;if(E.background&&($.style.background=E.background),E.container&&E.container instanceof Object&&(P.container=me({},L.container,E.container)),E.template){var R=Ne(E.template)?E.template:document.querySelector(E.template);P.template=R}return L=me({},L,P),T.forEach(function(j){j.dispatchEvent(be("medium-zoom:update",{detail:{zoom:O}}))}),O},i=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return e(me({},L,E))},s=function(){for(var E=arguments.length,P=Array(E),R=0;R<E;R++)P[R]=arguments[R];var j=P.reduce(function(C,Q){return[].concat(C,Lt(Q))},[]);return j.filter(function(C){return T.indexOf(C)===-1}).forEach(function(C){T.push(C),C.classList.add("medium-zoom-image")}),F.forEach(function(C){var Q=C.type,ee=C.listener,_e=C.options;j.forEach(function(de){de.addEventListener(Q,ee,_e)})}),O},u=function(){for(var E=arguments.length,P=Array(E),R=0;R<E;R++)P[R]=arguments[R];d.zoomed&&b();var j=P.length>0?P.reduce(function(C,Q){return[].concat(C,Lt(Q))},[]):T;return j.forEach(function(C){C.classList.remove("medium-zoom-image"),C.dispatchEvent(be("medium-zoom:detach",{detail:{zoom:O}}))}),T=T.filter(function(C){return j.indexOf(C)===-1}),O},v=function(E,P){var R=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return T.forEach(function(j){j.addEventListener("medium-zoom:"+E,P,R)}),F.push({type:"medium-zoom:"+E,listener:P,options:R}),O},f=function(E,P){var R=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return T.forEach(function(j){j.removeEventListener("medium-zoom:"+E,P,R)}),F=F.filter(function(j){return!(j.type==="medium-zoom:"+E&&j.listener.toString()===P.toString())}),O},m=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},P=E.target,R=function(){var C={width:document.documentElement.clientWidth,height:document.documentElement.clientHeight,left:0,top:0,right:0,bottom:0},Q=void 0,ee=void 0;if(L.container)if(L.container instanceof Object)C=me({},C,L.container),Q=C.width-C.left-C.right-L.margin*2,ee=C.height-C.top-C.bottom-L.margin*2;else{var _e=Ne(L.container)?L.container:document.querySelector(L.container),de=_e.getBoundingClientRect(),We=de.width,An=de.height,zn=de.left,Nn=de.top;C=me({},C,{width:We,height:An,left:zn,top:Nn})}Q=Q||C.width-L.margin*2,ee=ee||C.height-L.margin*2;var Le=d.zoomedHd||d.original,Dn=wt(Le)?Q:Le.naturalWidth||Q,Hn=wt(Le)?ee:Le.naturalHeight||ee,Pe=Le.getBoundingClientRect(),Rn=Pe.top,Mn=Pe.left,Ue=Pe.width,Ke=Pe.height,Bn=Math.min(Math.max(Ue,Dn),Q)/Ue,Fn=Math.min(Math.max(Ke,Hn),ee)/Ke,Ge=Math.min(Bn,Fn),jn=(-Mn+(Q-Ue)/2+L.margin+C.left)/Ge,Vn=(-Rn+(ee-Ke)/2+L.margin+C.top)/Ge,gt="scale("+Ge+") translate3d("+jn+"px, "+Vn+"px, 0)";d.zoomed.style.transform=gt,d.zoomedHd&&(d.zoomedHd.style.transform=gt)};return new o(function(j){if(P&&T.indexOf(P)===-1){j(O);return}var C=function We(){q=!1,d.zoomed.removeEventListener("transitionend",We),d.original.dispatchEvent(be("medium-zoom:opened",{detail:{zoom:O}})),j(O)};if(d.zoomed){j(O);return}if(P)d.original=P;else if(T.length>0){var Q=T;d.original=Q[0]}else{j(O);return}if(d.original.dispatchEvent(be("medium-zoom:open",{detail:{zoom:O}})),Z=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,q=!0,d.zoomed=No(d.original),document.body.appendChild($),L.template){var ee=Ne(L.template)?L.template:document.querySelector(L.template);d.template=document.createElement("div"),d.template.appendChild(ee.content.cloneNode(!0)),document.body.appendChild(d.template)}if(d.original.parentElement&&d.original.parentElement.tagName==="PICTURE"&&d.original.currentSrc&&(d.zoomed.src=d.original.currentSrc),document.body.appendChild(d.zoomed),window.requestAnimationFrame(function(){document.body.classList.add("medium-zoom--opened")}),d.original.classList.add("medium-zoom-image--hidden"),d.zoomed.classList.add("medium-zoom-image--opened"),d.zoomed.addEventListener("click",b),d.zoomed.addEventListener("transitionend",C),d.original.getAttribute("data-zoom-src")){d.zoomedHd=d.zoomed.cloneNode(),d.zoomedHd.removeAttribute("srcset"),d.zoomedHd.removeAttribute("sizes"),d.zoomedHd.removeAttribute("loading"),d.zoomedHd.src=d.zoomed.getAttribute("data-zoom-src"),d.zoomedHd.onerror=function(){clearInterval(_e),console.warn("Unable to reach the zoom image target "+d.zoomedHd.src),d.zoomedHd=null,R()};var _e=setInterval(function(){d.zoomedHd.complete&&(clearInterval(_e),d.zoomedHd.classList.add("medium-zoom-image--opened"),d.zoomedHd.addEventListener("click",b),document.body.appendChild(d.zoomedHd),R())},10)}else if(d.original.hasAttribute("srcset")){d.zoomedHd=d.zoomed.cloneNode(),d.zoomedHd.removeAttribute("sizes"),d.zoomedHd.removeAttribute("loading");var de=d.zoomedHd.addEventListener("load",function(){d.zoomedHd.removeEventListener("load",de),d.zoomedHd.classList.add("medium-zoom-image--opened"),d.zoomedHd.addEventListener("click",b),document.body.appendChild(d.zoomedHd),R()})}else R()})},b=function(){return new o(function(E){if(q||!d.original){E(O);return}var P=function R(){d.original.classList.remove("medium-zoom-image--hidden"),document.body.removeChild(d.zoomed),d.zoomedHd&&document.body.removeChild(d.zoomedHd),document.body.removeChild($),d.zoomed.classList.remove("medium-zoom-image--opened"),d.template&&document.body.removeChild(d.template),q=!1,d.zoomed.removeEventListener("transitionend",R),d.original.dispatchEvent(be("medium-zoom:closed",{detail:{zoom:O}})),d.original=null,d.zoomed=null,d.zoomedHd=null,d.template=null,E(O)};q=!0,document.body.classList.remove("medium-zoom--opened"),d.zoomed.style.transform="",d.zoomedHd&&(d.zoomedHd.style.transform=""),d.template&&(d.template.style.transition="opacity 150ms",d.template.style.opacity=0),d.original.dispatchEvent(be("medium-zoom:close",{detail:{zoom:O}})),d.zoomed.addEventListener("transitionend",P)})},k=function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},P=E.target;return d.original?b():m({target:P})},_=function(){return L},w=function(){return T},N=function(){return d.original},T=[],F=[],q=!1,Z=0,L=n,d={original:null,zoomed:null,zoomedHd:null,template:null};Object.prototype.toString.call(t)==="[object Object]"?L=t:(t||typeof t=="string")&&s(t),L=me({margin:0,background:"#fff",scrollOffset:40,container:null,template:null},L);var $=zo(L.background);document.addEventListener("click",r),document.addEventListener("keyup",c),document.addEventListener("scroll",a),window.addEventListener("resize",b);var O={open:m,close:b,toggle:k,update:l,clone:i,attach:s,detach:u,on:v,off:f,getOptions:_,getImages:w,getZoomedImage:N};return O};function Ho(e,t){t===void 0&&(t={});var n=t.insertAt;if(!(!e||typeof document>"u")){var o=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",n==="top"&&o.firstChild?o.insertBefore(r,o.firstChild):o.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}}var Ro=".medium-zoom-overlay{position:fixed;top:0;right:0;bottom:0;left:0;opacity:0;transition:opacity .3s;will-change:opacity}.medium-zoom--opened .medium-zoom-overlay{cursor:pointer;cursor:zoom-out;opacity:1}.medium-zoom-image{cursor:pointer;cursor:zoom-in;transition:transform .3s cubic-bezier(.2,0,.2,1)!important}.medium-zoom-image--hidden{visibility:hidden}.medium-zoom-image--opened{position:relative;cursor:pointer;cursor:zoom-out;will-change:transform}";Ho(Ro);const Mo=Do,Bo=Symbol("mediumZoom");const Fo=".theme-default-content > img, .theme-default-content :not(a) > img",jo={},Vo=300,qo=re({enhance({app:e,router:t}){const n=Mo(jo);n.refresh=(o=Fo)=>{n.detach(),n.attach(o)},e.provide(Bo,n),t.afterEach(()=>{setTimeout(()=>n.refresh(),Vo)})}});/**
 * NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT
 */const I={settings:{minimum:.08,easing:"ease",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,barSelector:'[role="bar"]',parent:"body",template:'<div class="bar" role="bar"></div>'},status:null,set:e=>{const t=I.isStarted();e=Je(e,I.settings.minimum,1),I.status=e===1?null:e;const n=I.render(!t),o=n.querySelector(I.settings.barSelector),r=I.settings.speed,a=I.settings.easing;return n.offsetWidth,Wo(c=>{Ce(o,{transform:"translate3d("+St(e)+"%,0,0)",transition:"all "+r+"ms "+a}),e===1?(Ce(n,{transition:"none",opacity:"1"}),n.offsetWidth,setTimeout(function(){Ce(n,{transition:"all "+r+"ms linear",opacity:"0"}),setTimeout(function(){I.remove(),c()},r)},r)):setTimeout(()=>c(),r)}),I},isStarted:()=>typeof I.status=="number",start:()=>{I.status||I.set(0);const e=()=>{setTimeout(()=>{I.status&&(I.trickle(),e())},I.settings.trickleSpeed)};return I.settings.trickle&&e(),I},done:e=>!e&&!I.status?I:I.inc(.3+.5*Math.random()).set(1),inc:e=>{let t=I.status;return t?(typeof e!="number"&&(e=(1-t)*Je(Math.random()*t,.1,.95)),t=Je(t+e,0,.994),I.set(t)):I.start()},trickle:()=>I.inc(Math.random()*I.settings.trickleRate),render:e=>{if(I.isRendered())return document.getElementById("nprogress");Et(document.documentElement,"nprogress-busy");const t=document.createElement("div");t.id="nprogress",t.innerHTML=I.settings.template;const n=t.querySelector(I.settings.barSelector),o=e?"-100":St(I.status||0),r=document.querySelector(I.settings.parent);return Ce(n,{transition:"all 0 linear",transform:"translate3d("+o+"%,0,0)"}),r!==document.body&&Et(r,"nprogress-custom-parent"),r==null||r.appendChild(t),t},remove:()=>{$t(document.documentElement,"nprogress-busy"),$t(document.querySelector(I.settings.parent),"nprogress-custom-parent");const e=document.getElementById("nprogress");e&&Uo(e)},isRendered:()=>!!document.getElementById("nprogress")},Je=(e,t,n)=>e<t?t:e>n?n:e,St=e=>(-1+e)*100,Wo=function(){const e=[];function t(){const n=e.shift();n&&n(t)}return function(n){e.push(n),e.length===1&&t()}}(),Ce=function(){const e=["Webkit","O","Moz","ms"],t={};function n(c){return c.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(l,i){return i.toUpperCase()})}function o(c){const l=document.body.style;if(c in l)return c;let i=e.length;const s=c.charAt(0).toUpperCase()+c.slice(1);let u;for(;i--;)if(u=e[i]+s,u in l)return u;return c}function r(c){return c=n(c),t[c]||(t[c]=o(c))}function a(c,l,i){l=r(l),c.style[l]=i}return function(c,l){for(const i in l){const s=l[i];s!==void 0&&Object.prototype.hasOwnProperty.call(l,i)&&a(c,i,s)}}}(),vn=(e,t)=>(typeof e=="string"?e:dt(e)).indexOf(" "+t+" ")>=0,Et=(e,t)=>{const n=dt(e),o=n+t;vn(n,t)||(e.className=o.substring(1))},$t=(e,t)=>{const n=dt(e);if(!vn(e,t))return;const o=n.replace(" "+t+" "," ");e.className=o.substring(1,o.length-1)},dt=e=>(" "+(e.className||"")+" ").replace(/\s+/gi," "),Uo=e=>{e&&e.parentNode&&e.parentNode.removeChild(e)};const Ko=()=>{J(()=>{const e=he(),t=new Set;t.add(e.currentRoute.value.path),e.beforeEach(n=>{t.has(n.path)||I.start()}),e.afterEach(n=>{t.add(n.path),I.done()})})},Go=re({setup(){Ko()}}),Jo=JSON.parse(`{"sidebarDepth":1,"logo":"./images/c192.png","contributorsText":"贡献者","lastUpdatedText":"上次更新","navbar":[{"text":"指南","link":"/"},{"text":"Github","link":"https://github.com/anzhiyu-c/anzhiyu-docs/"},{"text":"作者博客","link":"https://blog.anheyu.com/"}],"sidebar":[{"text":"简介","link":"/"},{"text":"快速上手","link":"/quick-start.html"},{"text":"页面配置","link":"/page-configuration.html"},{"text":"站点配置(一)","link":"/site-configuration1.html"},{"text":"站点配置(二)","link":"/site-configuration2.html"},{"text":"站点配置(三)","link":"/site-configuration3.html"},{"text":"站点配置(四)","link":"/site-configuration4.html"},{"text":"赞赏名单","link":"/reward-list.html"}],"locales":{"/":{"selectLanguageName":"English"}},"colorMode":"auto","colorModeSwitch":true,"repo":null,"selectLanguageText":"Languages","selectLanguageAriaLabel":"Select language","editLink":true,"editLinkText":"Edit this page","lastUpdated":true,"contributors":true,"notFound":["There's nothing here.","How did we get here?","That's a Four-Oh-Four.","Looks like we've got some broken links."],"backToHome":"Take me home","openInNewWindow":"open in new window","toggleColorMode":"toggle color mode","toggleSidebar":"toggle sidebar"}`),Qo=x(Jo),Yo=()=>Qo,fn=Symbol(""),Zo=()=>{const e=ce(fn);if(!e)throw new Error("useThemeLocaleData() is called without provider.");return e},Xo=(e,t)=>{var n;return{...e,...(n=e.locales)==null?void 0:n[t]}},er=re({enhance({app:e}){const t=Yo(),n=e._context.provides[st],o=g(()=>Xo(t.value,n.value));e.provide(fn,o),Object.defineProperties(e.config.globalProperties,{$theme:{get(){return t.value}},$themeLocale:{get(){return o.value}}})}}),tr=A({__name:"Badge",props:{type:{type:String,required:!1,default:"tip"},text:{type:String,required:!1,default:""},vertical:{type:String,required:!1,default:void 0}},setup(e){return(t,n)=>(h(),y("span",{class:ne(["badge",e.type]),style:Yt({verticalAlign:e.vertical})},[D(t.$slots,"default",{},()=>[ke(V(e.text),1)])],6))}}),nr=M(tr,[["__file","Badge.vue"]]),or=A({name:"CodeGroup",setup(e,{slots:t}){const n=x(-1),o=x([]),r=(l=n.value)=>{l<o.value.length-1?n.value=l+1:n.value=0,o.value[n.value].focus()},a=(l=n.value)=>{l>0?n.value=l-1:n.value=o.value.length-1,o.value[n.value].focus()},c=(l,i)=>{l.key===" "||l.key==="Enter"?(l.preventDefault(),n.value=i):l.key==="ArrowRight"?(l.preventDefault(),r(i)):l.key==="ArrowLeft"&&(l.preventDefault(),a(i))};return()=>{var i;const l=(((i=t.default)==null?void 0:i.call(t))||[]).filter(s=>s.type.name==="CodeGroupItem").map(s=>(s.props===null&&(s.props={}),s));return l.length===0?null:(n.value<0||n.value>l.length-1?(n.value=l.findIndex(s=>s.props.active===""||s.props.active===!0),n.value===-1&&(n.value=0)):l.forEach((s,u)=>{s.props.active=u===n.value}),S("div",{class:"code-group"},[S("div",{class:"code-group__nav"},S("ul",{class:"code-group__ul"},l.map((s,u)=>{const v=u===n.value;return S("li",{class:"code-group__li"},S("button",{ref:f=>{f&&(o.value[u]=f)},class:{"code-group__nav-tab":!0,"code-group__nav-tab-active":v},ariaPressed:v,ariaExpanded:v,onClick:()=>n.value=u,onKeydown:f=>c(f,u)},s.props.title))}))),l]))}}}),rr=["aria-selected"],ar=A({name:"CodeGroupItem"}),lr=A({...ar,props:{title:{type:String,required:!0},active:{type:Boolean,required:!1,default:!1}},setup(e){return(t,n)=>(h(),y("div",{class:ne(["code-group-item",{"code-group-item__active":e.active}]),"aria-selected":e.active},[D(t.$slots,"default")],10,rr))}}),sr=M(lr,[["__file","CodeGroupItem.vue"]]);var Ot;const pn=typeof window<"u",ir=e=>typeof e=="function",ur=e=>typeof e=="string",cr=()=>{};pn&&((Ot=window==null?void 0:window.navigator)!=null&&Ot.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function Ee(e){return typeof e=="function"?e():p(e)}function dr(e,t){function n(...o){return new Promise((r,a)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(r).catch(a)})}return n}const mn=e=>e();function vr(e=mn){const t=x(!0);function n(){t.value=!1}function o(){t.value=!0}const r=(...a)=>{t.value&&e(...a)};return{isActive:je(t),pause:n,resume:o,eventFilter:r}}function fr(e){return e}function hn(e){return Zt()?(Xt(e),!0):!1}function pr(e){return typeof e=="function"?g(e):x(e)}function mr(e,t=!0){rt()?J(e):t?e():Oe(e)}function hr(e=!1,t={}){const{truthyValue:n=!0,falsyValue:o=!1}=t,r=Un(e),a=x(e);function c(l){if(arguments.length)return a.value=l,a.value;{const i=Ee(n);return a.value=a.value===i?Ee(o):i,a.value}}return r?c:[a,c]}var Tt=Object.getOwnPropertySymbols,gr=Object.prototype.hasOwnProperty,_r=Object.prototype.propertyIsEnumerable,br=(e,t)=>{var n={};for(var o in e)gr.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&Tt)for(var o of Tt(e))t.indexOf(o)<0&&_r.call(e,o)&&(n[o]=e[o]);return n};function yr(e,t,n={}){const o=n,{eventFilter:r=mn}=o,a=br(o,["eventFilter"]);return oe(e,dr(r,t),a)}var kr=Object.defineProperty,wr=Object.defineProperties,Lr=Object.getOwnPropertyDescriptors,Re=Object.getOwnPropertySymbols,gn=Object.prototype.hasOwnProperty,_n=Object.prototype.propertyIsEnumerable,Pt=(e,t,n)=>t in e?kr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Sr=(e,t)=>{for(var n in t||(t={}))gn.call(t,n)&&Pt(e,n,t[n]);if(Re)for(var n of Re(t))_n.call(t,n)&&Pt(e,n,t[n]);return e},Er=(e,t)=>wr(e,Lr(t)),$r=(e,t)=>{var n={};for(var o in e)gn.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&Re)for(var o of Re(e))t.indexOf(o)<0&&_n.call(e,o)&&(n[o]=e[o]);return n};function Or(e,t,n={}){const o=n,{eventFilter:r}=o,a=$r(o,["eventFilter"]),{eventFilter:c,pause:l,resume:i,isActive:s}=vr(r);return{stop:yr(e,t,Er(Sr({},a),{eventFilter:c})),pause:l,resume:i,isActive:s}}function Tr(e){var t;const n=Ee(e);return(t=n==null?void 0:n.$el)!=null?t:n}const Me=pn?window:void 0;function xt(...e){let t,n,o,r;if(ur(e[0])||Array.isArray(e[0])?([n,o,r]=e,t=Me):[t,n,o,r]=e,!t)return cr;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const a=[],c=()=>{a.forEach(u=>u()),a.length=0},l=(u,v,f,m)=>(u.addEventListener(v,f,m),()=>u.removeEventListener(v,f,m)),i=oe(()=>[Tr(t),Ee(r)],([u,v])=>{c(),u&&a.push(...n.flatMap(f=>o.map(m=>l(u,f,m,v))))},{immediate:!0,flush:"post"}),s=()=>{i(),c()};return hn(s),s}function Pr(e,t=!1){const n=x(),o=()=>n.value=Boolean(e());return o(),mr(o,t),n}function xr(e,t={}){const{window:n=Me}=t,o=Pr(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let r;const a=x(!1),c=()=>{r&&("removeEventListener"in r?r.removeEventListener("change",l):r.removeListener(l))},l=()=>{o.value&&(c(),r=n.matchMedia(pr(e).value),a.value=r.matches,"addEventListener"in r?r.addEventListener("change",l):r.addListener(l))};return Kn(l),hn(()=>c()),a}const Xe=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},et="__vueuse_ssr_handlers__";Xe[et]=Xe[et]||{};const Cr=Xe[et];function Ir(e,t){return Cr[e]||t}function Ar(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}var zr=Object.defineProperty,Ct=Object.getOwnPropertySymbols,Nr=Object.prototype.hasOwnProperty,Dr=Object.prototype.propertyIsEnumerable,It=(e,t,n)=>t in e?zr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,At=(e,t)=>{for(var n in t||(t={}))Nr.call(t,n)&&It(e,n,t[n]);if(Ct)for(var n of Ct(t))Dr.call(t,n)&&It(e,n,t[n]);return e};const Hr={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},zt="vueuse-storage";function Rr(e,t,n,o={}){var r;const{flush:a="pre",deep:c=!0,listenToStorageChanges:l=!0,writeDefaults:i=!0,mergeDefaults:s=!1,shallow:u,window:v=Me,eventFilter:f,onError:m=d=>{console.error(d)}}=o,b=(u?en:x)(t);if(!n)try{n=Ir("getDefaultStorage",()=>{var d;return(d=Me)==null?void 0:d.localStorage})()}catch(d){m(d)}if(!n)return b;const k=Ee(t),_=Ar(k),w=(r=o.serializer)!=null?r:Hr[_],{pause:N,resume:T}=Or(b,()=>F(b.value),{flush:a,deep:c,eventFilter:f});return v&&l&&(xt(v,"storage",L),xt(v,zt,Z)),L(),b;function F(d){try{if(d==null)n.removeItem(e);else{const $=w.write(d),O=n.getItem(e);O!==$&&(n.setItem(e,$),v&&v.dispatchEvent(new CustomEvent(zt,{detail:{key:e,oldValue:O,newValue:$,storageArea:n}})))}}catch($){m($)}}function q(d){const $=d?d.newValue:n.getItem(e);if($==null)return i&&k!==null&&n.setItem(e,w.write(k)),k;if(!d&&s){const O=w.read($);return ir(s)?s(O,k):_==="object"&&!Array.isArray(O)?At(At({},k),O):O}else return typeof $!="string"?$:w.read($)}function Z(d){L(d.detail)}function L(d){if(!(d&&d.storageArea!==n)){if(d&&d.key==null){b.value=k;return}if(!(d&&d.key!==e)){N();try{b.value=q(d)}catch($){m($)}finally{d?Oe(T):T()}}}}}function Mr(e){return xr("(prefers-color-scheme: dark)",e)}var Nt;(function(e){e.UP="UP",e.RIGHT="RIGHT",e.DOWN="DOWN",e.LEFT="LEFT",e.NONE="NONE"})(Nt||(Nt={}));var Br=Object.defineProperty,Dt=Object.getOwnPropertySymbols,Fr=Object.prototype.hasOwnProperty,jr=Object.prototype.propertyIsEnumerable,Ht=(e,t,n)=>t in e?Br(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Vr=(e,t)=>{for(var n in t||(t={}))Fr.call(t,n)&&Ht(e,n,t[n]);if(Dt)for(var n of Dt(t))jr.call(t,n)&&Ht(e,n,t[n]);return e};const qr={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};Vr({linear:fr},qr);const Y=()=>Zo(),bn=Symbol(""),vt=()=>{const e=ce(bn);if(!e)throw new Error("useDarkMode() is called without provider.");return e},Wr=()=>{const e=Y(),t=Mr(),n=Rr("vuepress-color-scheme",e.value.colorMode),o=g({get(){return e.value.colorModeSwitch?n.value==="auto"?t.value:n.value==="dark":e.value.colorMode==="dark"},set(r){r===t.value?n.value="auto":n.value=r?"dark":"light"}});at(bn,o),Ur(o)},Ur=e=>{const t=(n=e.value)=>{const o=window==null?void 0:window.document.querySelector("html");o==null||o.classList.toggle("dark",n)};J(()=>{oe(e,t,{immediate:!0})}),tn(()=>t())},yn=(...e)=>{const n=he().resolve(...e),o=n.matched[n.matched.length-1];if(!(o!=null&&o.redirect))return n;const{redirect:r}=o,a=Gn(r)?r(n):r,c=te(a)?{path:a}:a;return yn({hash:n.hash,query:n.query,params:n.params,...c})},ft=e=>{const t=yn(encodeURI(e));return{text:t.meta.title||e,link:t.name==="404"?e:t.fullPath}};let Qe=null,Se=null;const Kr={wait:()=>Qe,pending:()=>{Qe=new Promise(e=>Se=e)},resolve:()=>{Se==null||Se(),Qe=null,Se=null}},kn=()=>Kr,wn=Symbol("sidebarItems"),pt=()=>{const e=ce(wn);if(!e)throw new Error("useSidebarItems() is called without provider.");return e},Gr=()=>{const e=Y(),t=se(),n=g(()=>Jr(t.value,e.value));at(wn,n)},Jr=(e,t)=>{const n=e.sidebar??t.sidebar??"auto",o=e.sidebarDepth??t.sidebarDepth??2;return e.home||n===!1?[]:n==="auto"?Yr(o):Ve(n)?Ln(n,o):lt(n)?Zr(n,o):[]},Qr=(e,t)=>({text:e.title,link:e.link,children:mt(e.children,t)}),mt=(e,t)=>t>0?e.map(n=>Qr(n,t-1)):[],Yr=e=>{const t=fe();return[{text:t.value.title,children:mt(t.value.headers,e)}]},Ln=(e,t)=>{const n=ge(),o=fe(),r=a=>{var l;let c;if(te(a)?c=ft(a):c=a,c.children)return{...c,children:c.children.map(i=>r(i))};if(c.link===n.path){const i=((l=o.value.headers[0])==null?void 0:l.level)===1?o.value.headers[0].children:o.value.headers;return{...c,children:mt(i,t)}}return c};return e.map(a=>r(a))},Zr=(e,t)=>{const n=ge(),o=Jt(e,n.path),r=e[o]??[];return Ln(r,t)},Xr={},ea={class:"theme-default-content"};function ta(e,t){const n=ue("Content");return h(),y("div",ea,[H(n)])}const na=M(Xr,[["render",ta],["__file","HomeContent.vue"]]),oa={key:0,class:"features"},ra=A({__name:"HomeFeatures",setup(e){const t=se(),n=g(()=>Ve(t.value.features)?t.value.features:[]);return(o,r)=>p(n).length?(h(),y("div",oa,[(h(!0),y(X,null,pe(p(n),a=>(h(),y("div",{key:a.title,class:"feature"},[z("h2",null,V(a.title),1),z("p",null,V(a.details),1)]))),128))])):B("v-if",!0)}}),aa=M(ra,[["__file","HomeFeatures.vue"]]),la=["innerHTML"],sa=["textContent"],ia=A({__name:"HomeFooter",setup(e){const t=se(),n=g(()=>t.value.footer),o=g(()=>t.value.footerHtml);return(r,a)=>p(n)?(h(),y(X,{key:0},[B(" eslint-disable-next-line vue/no-v-html "),p(o)?(h(),y("div",{key:0,class:"footer",innerHTML:p(n)},null,8,la)):(h(),y("div",{key:1,class:"footer",textContent:V(p(n))},null,8,sa))],64)):B("v-if",!0)}}),ua=M(ia,[["__file","HomeFooter.vue"]]),ca=["href","rel","target","aria-label"],da=A({inheritAttrs:!1}),va=A({...da,__name:"AutoLink",props:{item:{type:Object,required:!0}},setup(e){const t=e,n=ge(),o=mo(),{item:r}=qe(t),a=g(()=>$e(r.value.link)),c=g(()=>Jn(r.value.link)||Qn(r.value.link)),l=g(()=>{if(!c.value){if(r.value.target)return r.value.target;if(a.value)return"_blank"}}),i=g(()=>l.value==="_blank"),s=g(()=>!a.value&&!c.value&&!i.value),u=g(()=>{if(!c.value){if(r.value.rel)return r.value.rel;if(i.value)return"noopener noreferrer"}}),v=g(()=>r.value.ariaLabel||r.value.text),f=g(()=>{const k=Object.keys(o.value.locales);return k.length?!k.some(_=>_===r.value.link):r.value.link!=="/"}),m=g(()=>f.value?n.path.startsWith(r.value.link):!1),b=g(()=>s.value?r.value.activeMatch?new RegExp(r.value.activeMatch).test(n.path):m.value:!1);return(k,_)=>{const w=ue("RouterLink"),N=ue("AutoLinkExternalIcon");return p(s)?(h(),U(w,_t({key:0,class:{"router-link-active":p(b)},to:p(r).link,"aria-label":p(v)},k.$attrs),{default:G(()=>[D(k.$slots,"before"),ke(" "+V(p(r).text)+" ",1),D(k.$slots,"after")]),_:3},16,["class","to","aria-label"])):(h(),y("a",_t({key:1,class:"external-link",href:p(r).link,rel:p(u),target:p(l),"aria-label":p(v)},k.$attrs),[D(k.$slots,"before"),ke(" "+V(p(r).text)+" ",1),p(i)?(h(),U(N,{key:0})):B("v-if",!0),D(k.$slots,"after")],16,ca))}}}),ie=M(va,[["__file","AutoLink.vue"]]),fa={class:"hero"},pa={key:0,id:"main-title"},ma={key:1,class:"description"},ha={key:2,class:"actions"},ga=A({__name:"HomeHero",setup(e){const t=se(),n=it(),o=vt(),r=g(()=>o.value&&t.value.heroImageDark!==void 0?t.value.heroImageDark:t.value.heroImage),a=g(()=>t.value.heroAlt||l.value||"hero"),c=g(()=>t.value.heroHeight||280),l=g(()=>t.value.heroText===null?null:t.value.heroText||n.value.title||"Hello"),i=g(()=>t.value.tagline===null?null:t.value.tagline||n.value.description||"Welcome to your VuePress site"),s=g(()=>Ve(t.value.actions)?t.value.actions.map(({text:v,link:f,type:m="primary"})=>({text:v,link:f,type:m})):[]),u=()=>{if(!r.value)return null;const v=S("img",{src:ct(r.value),alt:a.value,height:c.value});return t.value.heroImageDark===void 0?v:S(ut,()=>v)};return(v,f)=>(h(),y("header",fa,[H(u),p(l)?(h(),y("h1",pa,V(p(l)),1)):B("v-if",!0),p(i)?(h(),y("p",ma,V(p(i)),1)):B("v-if",!0),p(s).length?(h(),y("p",ha,[(h(!0),y(X,null,pe(p(s),m=>(h(),U(ie,{key:m.text,class:ne(["action-button",[m.type]]),item:m},null,8,["class","item"]))),128))])):B("v-if",!0)]))}}),_a=M(ga,[["__file","HomeHero.vue"]]),ba={class:"home"},ya=A({__name:"Home",setup(e){return(t,n)=>(h(),y("main",ba,[H(_a),H(aa),H(na),H(ua)]))}}),ka=M(ya,[["__file","Home.vue"]]),wa=A({__name:"NavbarBrand",setup(e){const t=we(),n=it(),o=Y(),r=vt(),a=g(()=>o.value.home||t.value),c=g(()=>n.value.title),l=g(()=>r.value&&o.value.logoDark!==void 0?o.value.logoDark:o.value.logo),i=()=>{if(!l.value)return null;const s=S("img",{class:"logo",src:ct(l.value),alt:c.value});return o.value.logoDark===void 0?s:S(ut,()=>s)};return(s,u)=>{const v=ue("RouterLink");return h(),U(v,{to:p(a)},{default:G(()=>[H(i),p(c)?(h(),y("span",{key:0,class:ne(["site-name",{"can-hide":p(l)}])},V(p(c)),3)):B("v-if",!0)]),_:1},8,["to"])}}}),La=M(wa,[["__file","NavbarBrand.vue"]]),Sa=A({__name:"DropdownTransition",setup(e){const t=o=>{o.style.height=o.scrollHeight+"px"},n=o=>{o.style.height=""};return(o,r)=>(h(),U(ot,{name:"dropdown",onEnter:t,onAfterEnter:n,onBeforeLeave:t},{default:G(()=>[D(o.$slots,"default")]),_:3}))}}),Sn=M(Sa,[["__file","DropdownTransition.vue"]]),Ea=["aria-label"],$a={class:"title"},Oa=z("span",{class:"arrow down"},null,-1),Ta=["aria-label"],Pa={class:"title"},xa={class:"navbar-dropdown"},Ca={class:"navbar-dropdown-subtitle"},Ia={key:1},Aa={class:"navbar-dropdown-subitem-wrapper"},za=A({__name:"NavbarDropdown",props:{item:{type:Object,required:!0}},setup(e){const t=e,{item:n}=qe(t),o=g(()=>n.value.ariaLabel||n.value.text),r=x(!1),a=ge();oe(()=>a.path,()=>{r.value=!1});const c=i=>{i.detail===0?r.value=!r.value:r.value=!1},l=(i,s)=>s[s.length-1]===i;return(i,s)=>(h(),y("div",{class:ne(["navbar-dropdown-wrapper",{open:r.value}])},[z("button",{class:"navbar-dropdown-title",type:"button","aria-label":p(o),onClick:c},[z("span",$a,V(p(n).text),1),Oa],8,Ea),z("button",{class:"navbar-dropdown-title-mobile",type:"button","aria-label":p(o),onClick:s[0]||(s[0]=u=>r.value=!r.value)},[z("span",Pa,V(p(n).text),1),z("span",{class:ne(["arrow",r.value?"down":"right"])},null,2)],8,Ta),H(Sn,null,{default:G(()=>[De(z("ul",xa,[(h(!0),y(X,null,pe(p(n).children,u=>(h(),y("li",{key:u.text,class:"navbar-dropdown-item"},[u.children?(h(),y(X,{key:0},[z("h4",Ca,[u.link?(h(),U(ie,{key:0,item:u,onFocusout:v=>l(u,p(n).children)&&u.children.length===0&&(r.value=!1)},null,8,["item","onFocusout"])):(h(),y("span",Ia,V(u.text),1))]),z("ul",Aa,[(h(!0),y(X,null,pe(u.children,v=>(h(),y("li",{key:v.link,class:"navbar-dropdown-subitem"},[H(ie,{item:v,onFocusout:f=>l(v,u.children)&&l(u,p(n).children)&&(r.value=!1)},null,8,["item","onFocusout"])]))),128))])],64)):(h(),U(ie,{key:1,item:u,onFocusout:v=>l(u,p(n).children)&&(r.value=!1)},null,8,["item","onFocusout"]))]))),128))],512),[[He,r.value]])]),_:1})],2))}}),Na=M(za,[["__file","NavbarDropdown.vue"]]),Rt=e=>decodeURI(e).replace(/#.*$/,"").replace(/(index)?\.(md|html)$/,""),Da=(e,t)=>{if(t.hash===e)return!0;const n=Rt(t.path),o=Rt(e);return n===o},En=(e,t)=>e.link&&Da(e.link,t)?!0:e.children?e.children.some(n=>En(n,t)):!1,$n=e=>!$e(e)||/github\.com/.test(e)?"GitHub":/bitbucket\.org/.test(e)?"Bitbucket":/gitlab\.com/.test(e)?"GitLab":/gitee\.com/.test(e)?"Gitee":null,Ha={GitHub:":repo/edit/:branch/:path",GitLab:":repo/-/edit/:branch/:path",Gitee:":repo/edit/:branch/:path",Bitbucket:":repo/src/:branch/:path?mode=edit&spa=0&at=:branch&fileviewer=file-view-default"},Ra=({docsRepo:e,editLinkPattern:t})=>{if(t)return t;const n=$n(e);return n!==null?Ha[n]:null},Ma=({docsRepo:e,docsBranch:t,docsDir:n,filePathRelative:o,editLinkPattern:r})=>{if(!o)return null;const a=Ra({docsRepo:e,editLinkPattern:r});return a?a.replace(/:repo/,$e(e)?e:`https://github.com/${e}`).replace(/:branch/,t).replace(/:path/,Qt(`${nn(n)}/${o}`)):null},Ba={key:0,class:"navbar-items"},Fa=A({__name:"NavbarItems",setup(e){const t=()=>{const u=he(),v=we(),f=it(),m=Y();return g(()=>{const b=Object.keys(f.value.locales);if(b.length<2)return[];const k=u.currentRoute.value.path,_=u.currentRoute.value.fullPath;return[{text:m.value.selectLanguageText??"unknown language",ariaLabel:m.value.selectLanguageAriaLabel??m.value.selectLanguageText??"unknown language",children:b.map(N=>{var d,$;const T=((d=f.value.locales)==null?void 0:d[N])??{},F=(($=m.value.locales)==null?void 0:$[N])??{},q=`${T.lang}`,Z=F.selectLanguageName??q;let L;if(q===f.value.lang)L=_;else{const O=k.replace(v.value,N);u.getRoutes().some(K=>K.path===O)?L=_.replace(k,O):L=F.home??N}return{text:Z,link:L}})}]})},n=()=>{const u=Y(),v=g(()=>u.value.repo),f=g(()=>v.value?$n(v.value):null),m=g(()=>v.value&&!$e(v.value)?`https://github.com/${v.value}`:v.value),b=g(()=>m.value?u.value.repoLabel?u.value.repoLabel:f.value===null?"Source":f.value:null);return g(()=>!m.value||!b.value?[]:[{text:b.value,link:m.value}])},o=u=>te(u)?ft(u):u.children?{...u,children:u.children.map(o)}:u,r=()=>{const u=Y();return g(()=>(u.value.navbar||[]).map(o))},a=x(!1),c=r(),l=t(),i=n(),s=g(()=>[...c.value,...l.value,...i.value]);return J(()=>{const v=()=>{window.innerWidth<719?a.value=!0:a.value=!1};v(),window.addEventListener("resize",v,!1),window.addEventListener("orientationchange",v,!1)}),(u,v)=>p(s).length?(h(),y("nav",Ba,[(h(!0),y(X,null,pe(p(s),f=>(h(),y("div",{key:f.text,class:"navbar-item"},[f.children?(h(),U(Na,{key:0,item:f,class:ne(a.value?"mobile":"")},null,8,["item","class"])):(h(),U(ie,{key:1,item:f},null,8,["item"]))]))),128))])):B("v-if",!0)}}),On=M(Fa,[["__file","NavbarItems.vue"]]),ja=["title"],Va={class:"icon",focusable:"false",viewBox:"0 0 32 32"},qa=Yn('<path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path>',9),Wa=[qa],Ua={class:"icon",focusable:"false",viewBox:"0 0 32 32"},Ka=z("path",{d:"M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z",fill:"currentColor"},null,-1),Ga=[Ka],Ja=A({__name:"ToggleColorModeButton",setup(e){const t=Y(),n=vt(),o=()=>{n.value=!n.value};return(r,a)=>(h(),y("button",{class:"toggle-color-mode-button",title:p(t).toggleColorMode,onClick:o},[De((h(),y("svg",Va,Wa,512)),[[He,!p(n)]]),De((h(),y("svg",Ua,Ga,512)),[[He,p(n)]])],8,ja))}}),Qa=M(Ja,[["__file","ToggleColorModeButton.vue"]]),Ya=["title"],Za=z("div",{class:"icon","aria-hidden":"true"},[z("span"),z("span"),z("span")],-1),Xa=[Za],el=A({__name:"ToggleSidebarButton",emits:["toggle"],setup(e){const t=Y();return(n,o)=>(h(),y("div",{class:"toggle-sidebar-button",title:p(t).toggleSidebar,"aria-expanded":"false",role:"button",tabindex:"0",onClick:o[0]||(o[0]=r=>n.$emit("toggle"))},Xa,8,Ya))}}),tl=M(el,[["__file","ToggleSidebarButton.vue"]]),nl=A({__name:"Navbar",emits:["toggle-sidebar"],setup(e){const t=Y(),n=x(null),o=x(null),r=x(0),a=g(()=>r.value?{maxWidth:r.value+"px"}:{});J(()=>{const i=c(n.value,"paddingLeft")+c(n.value,"paddingRight"),s=()=>{var u;window.innerWidth<719?r.value=0:r.value=n.value.offsetWidth-i-(((u=o.value)==null?void 0:u.offsetWidth)||0)};s(),window.addEventListener("resize",s,!1),window.addEventListener("orientationchange",s,!1)});function c(l,i){var v,f,m;const s=(m=(f=(v=l==null?void 0:l.ownerDocument)==null?void 0:v.defaultView)==null?void 0:f.getComputedStyle(l,null))==null?void 0:m[i],u=Number.parseInt(s,10);return Number.isNaN(u)?0:u}return(l,i)=>{const s=ue("NavbarSearch");return h(),y("header",{ref_key:"navbar",ref:n,class:"navbar"},[H(tl,{onToggle:i[0]||(i[0]=u=>l.$emit("toggle-sidebar"))}),z("span",{ref_key:"navbarBrand",ref:o},[H(La)],512),z("div",{class:"navbar-items-wrapper",style:Yt(p(a))},[D(l.$slots,"before"),H(On,{class:"can-hide"}),D(l.$slots,"after"),p(t).colorModeSwitch?(h(),U(Qa,{key:0})):B("v-if",!0),H(s)],4)],512)}}}),ol=M(nl,[["__file","Navbar.vue"]]),rl={class:"page-meta"},al={key:0,class:"meta-item edit-link"},ll={key:1,class:"meta-item last-updated"},sl={class:"meta-item-label"},il={class:"meta-item-info"},ul={key:2,class:"meta-item contributors"},cl={class:"meta-item-label"},dl={class:"meta-item-info"},vl=["title"],fl=A({__name:"PageMeta",setup(e){const t=()=>{const i=Y(),s=fe(),u=se();return g(()=>{if(!(u.value.editLink??i.value.editLink??!0))return null;const{repo:f,docsRepo:m=f,docsBranch:b="main",docsDir:k="",editLinkText:_}=i.value;if(!m)return null;const w=Ma({docsRepo:m,docsBranch:b,docsDir:k,filePathRelative:s.value.filePathRelative,editLinkPattern:u.value.editLinkPattern??i.value.editLinkPattern});return w?{text:_??"Edit this page",link:w}:null})},n=()=>{const i=Y(),s=fe(),u=se();return g(()=>{var m,b;return!(u.value.lastUpdated??i.value.lastUpdated??!0)||!((m=s.value.git)!=null&&m.updatedTime)?null:new Date((b=s.value.git)==null?void 0:b.updatedTime).toLocaleString()})},o=()=>{const i=Y(),s=fe(),u=se();return g(()=>{var f;return u.value.contributors??i.value.contributors??!0?((f=s.value.git)==null?void 0:f.contributors)??null:null})},r=Y(),a=t(),c=n(),l=o();return(i,s)=>{const u=ue("ClientOnly");return h(),y("footer",rl,[p(a)?(h(),y("div",al,[H(ie,{class:"meta-item-label",item:p(a)},null,8,["item"])])):B("v-if",!0),p(c)?(h(),y("div",ll,[z("span",sl,V(p(r).lastUpdatedText)+": ",1),H(u,null,{default:G(()=>[z("span",il,V(p(c)),1)]),_:1})])):B("v-if",!0),p(l)&&p(l).length?(h(),y("div",ul,[z("span",cl,V(p(r).contributorsText)+": ",1),z("span",dl,[(h(!0),y(X,null,pe(p(l),(v,f)=>(h(),y(X,{key:f},[z("span",{class:"contributor",title:`email: ${v.email}`},V(v.name),9,vl),f!==p(l).length-1?(h(),y(X,{key:0},[ke(", ")],64)):B("v-if",!0)],64))),128))])])):B("v-if",!0)])}}}),pl=M(fl,[["__file","PageMeta.vue"]]),ml={key:0,class:"page-nav"},hl={class:"inner"},gl={key:0,class:"prev"},_l={key:1,class:"next"},bl=A({__name:"PageNav",setup(e){const t=i=>i===!1?null:te(i)?ft(i):lt(i)?i:!1,n=(i,s,u)=>{const v=i.findIndex(f=>f.link===s);if(v!==-1){const f=i[v+u];return f!=null&&f.link?f:null}for(const f of i)if(f.children){const m=n(f.children,s,u);if(m)return m}return null},o=se(),r=pt(),a=ge(),c=g(()=>{const i=t(o.value.prev);return i!==!1?i:n(r.value,a.path,-1)}),l=g(()=>{const i=t(o.value.next);return i!==!1?i:n(r.value,a.path,1)});return(i,s)=>p(c)||p(l)?(h(),y("nav",ml,[z("p",hl,[p(c)?(h(),y("span",gl,[H(ie,{item:p(c)},null,8,["item"])])):B("v-if",!0),p(l)?(h(),y("span",_l,[H(ie,{item:p(l)},null,8,["item"])])):B("v-if",!0)])])):B("v-if",!0)}}),yl=M(bl,[["__file","PageNav.vue"]]),kl={class:"page"},wl={class:"theme-default-content"},Ll=A({__name:"Page",setup(e){return(t,n)=>{const o=ue("Content");return h(),y("main",kl,[D(t.$slots,"top"),z("div",wl,[D(t.$slots,"content-top"),H(o),D(t.$slots,"content-bottom")]),H(pl),H(yl),D(t.$slots,"bottom")])}}}),Sl=M(Ll,[["__file","Page.vue"]]),El=["onKeydown"],$l={class:"sidebar-item-children"},Ol=A({__name:"SidebarItem",props:{item:{type:Object,required:!0},depth:{type:Number,required:!1,default:0}},setup(e){const t=e,{item:n,depth:o}=qe(t),r=ge(),a=he(),c=g(()=>En(n.value,r)),l=g(()=>({"sidebar-item":!0,"sidebar-heading":o.value===0,active:c.value,collapsible:n.value.collapsible})),i=g(()=>n.value.collapsible?c.value:!0),[s,u]=hr(i.value),v=m=>{n.value.collapsible&&(m.preventDefault(),u())},f=a.afterEach(m=>{Oe(()=>{s.value=i.value})});return nt(()=>{f()}),(m,b)=>{var _;const k=ue("SidebarItem",!0);return h(),y("li",null,[p(n).link?(h(),U(ie,{key:0,class:ne(p(l)),item:p(n)},null,8,["class","item"])):(h(),y("p",{key:1,tabindex:"0",class:ne(p(l)),onClick:v,onKeydown:Zn(v,["enter"])},[ke(V(p(n).text)+" ",1),p(n).collapsible?(h(),y("span",{key:0,class:ne(["arrow",p(s)?"down":"right"])},null,2)):B("v-if",!0)],42,El)),(_=p(n).children)!=null&&_.length?(h(),U(Sn,{key:2},{default:G(()=>[De(z("ul",$l,[(h(!0),y(X,null,pe(p(n).children,w=>(h(),U(k,{key:`${p(o)}${w.text}${w.link}`,item:w,depth:p(o)+1},null,8,["item","depth"]))),128))],512),[[He,p(s)]])]),_:1})):B("v-if",!0)])}}}),Tl=M(Ol,[["__file","SidebarItem.vue"]]),Pl={key:0,class:"sidebar-items"},xl=A({__name:"SidebarItems",setup(e){const t=ge(),n=pt();return J(()=>{oe(()=>t.hash,o=>{const r=document.querySelector(".sidebar");if(!r)return;const a=document.querySelector(`.sidebar a.sidebar-item[href="${t.path}${o}"]`);if(!a)return;const{top:c,height:l}=r.getBoundingClientRect(),{top:i,height:s}=a.getBoundingClientRect();i<c?a.scrollIntoView(!0):i+s>c+l&&a.scrollIntoView(!1)})}),(o,r)=>p(n).length?(h(),y("ul",Pl,[(h(!0),y(X,null,pe(p(n),a=>(h(),U(Tl,{key:`${a.text}${a.link}`,item:a},null,8,["item"]))),128))])):B("v-if",!0)}}),Cl=M(xl,[["__file","SidebarItems.vue"]]),Il={class:"sidebar"},Al=A({__name:"Sidebar",setup(e){return(t,n)=>(h(),y("aside",Il,[H(On),D(t.$slots,"top"),H(Cl),D(t.$slots,"bottom")]))}}),zl=M(Al,[["__file","Sidebar.vue"]]),Nl=A({__name:"Layout",setup(e){const t=fe(),n=se(),o=Y(),r=g(()=>n.value.navbar!==!1&&o.value.navbar!==!1),a=pt(),c=x(!1),l=_=>{c.value=typeof _=="boolean"?_:!c.value},i={x:0,y:0},s=_=>{i.x=_.changedTouches[0].clientX,i.y=_.changedTouches[0].clientY},u=_=>{const w=_.changedTouches[0].clientX-i.x,N=_.changedTouches[0].clientY-i.y;Math.abs(w)>Math.abs(N)&&Math.abs(w)>40&&(w>0&&i.x<=80?l(!0):l(!1))},v=g(()=>[{"no-navbar":!r.value,"no-sidebar":!a.value.length,"sidebar-open":c.value},n.value.pageClass]);let f;J(()=>{f=he().afterEach(()=>{l(!1)})}),tn(()=>{f()});const m=kn(),b=m.resolve,k=m.pending;return(_,w)=>(h(),y("div",{class:ne(["theme-container",p(v)]),onTouchstart:s,onTouchend:u},[D(_.$slots,"navbar",{},()=>[p(r)?(h(),U(ol,{key:0,onToggleSidebar:l},{before:G(()=>[D(_.$slots,"navbar-before")]),after:G(()=>[D(_.$slots,"navbar-after")]),_:3})):B("v-if",!0)]),z("div",{class:"sidebar-mask",onClick:w[0]||(w[0]=N=>l(!1))}),D(_.$slots,"sidebar",{},()=>[H(zl,null,{top:G(()=>[D(_.$slots,"sidebar-top")]),bottom:G(()=>[D(_.$slots,"sidebar-bottom")]),_:3})]),D(_.$slots,"page",{},()=>[p(n).home?(h(),U(ka,{key:0})):(h(),U(ot,{key:1,name:"fade-slide-y",mode:"out-in",onBeforeEnter:p(b),onBeforeLeave:p(k)},{default:G(()=>[(h(),U(Sl,{key:p(t).path},{top:G(()=>[D(_.$slots,"page-top")]),"content-top":G(()=>[D(_.$slots,"page-content-top")]),"content-bottom":G(()=>[D(_.$slots,"page-content-bottom")]),bottom:G(()=>[D(_.$slots,"page-bottom")]),_:3}))]),_:3},8,["onBeforeEnter","onBeforeLeave"]))])],34))}}),Dl=M(Nl,[["__file","Layout.vue"]]),Hl={class:"theme-container"},Rl={class:"page"},Ml={class:"theme-default-content"},Bl=z("h1",null,"404",-1),Fl=A({__name:"NotFound",setup(e){const t=we(),n=Y(),o=n.value.notFound??["Not Found"],r=()=>o[Math.floor(Math.random()*o.length)],a=n.value.home??t.value,c=n.value.backToHome??"Back to home";return(l,i)=>{const s=ue("RouterLink");return h(),y("div",Hl,[z("main",Rl,[z("div",Ml,[Bl,z("blockquote",null,V(r()),1),H(s,{to:p(a)},{default:G(()=>[ke(V(p(c)),1)]),_:1},8,["to"])])])])}}}),jl=M(Fl,[["__file","NotFound.vue"]]);const Vl=re({enhance({app:e,router:t}){e.component("Badge",nr),e.component("CodeGroup",or),e.component("CodeGroupItem",sr),e.component("AutoLinkExternalIcon",()=>{const o=e.component("ExternalLinkIcon");return o?S(o):null}),e.component("NavbarSearch",()=>{const o=e.component("Docsearch")||e.component("SearchBox");return o?S(o):null});const n=t.options.scrollBehavior;t.options.scrollBehavior=async(...o)=>(await kn().wait(),n(...o))},setup(){Wr(),Gr()},layouts:{Layout:Dl,NotFound:jl}}),ql=e=>e instanceof Element?document.activeElement===e&&(["TEXTAREA","SELECT","INPUT"].includes(e.tagName)||e.hasAttribute("contenteditable")):!1,Wl=(e,t)=>t.some(n=>{if(te(n))return n===e.key;const{key:o,ctrl:r=!1,shift:a=!1,alt:c=!1}=n;return o===e.key&&r===e.ctrlKey&&a===e.shiftKey&&c===e.altKey}),Ul=/[^\x00-\x7F]/,Kl=e=>e.split(/\s+/g).map(t=>t.trim()).filter(t=>!!t),Mt=e=>e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),Bt=(e,t)=>{const n=t.join(" "),o=Kl(e);if(Ul.test(e))return o.some(c=>n.toLowerCase().indexOf(c)>-1);const r=e.endsWith(" ");return new RegExp(o.map((c,l)=>o.length===l+1&&!r?`(?=.*\\b${Mt(c)})`:`(?=.*\\b${Mt(c)}\\b)`).join("")+".+","gi").test(n)},Gl=({input:e,hotKeys:t})=>{if(t.value.length===0)return;const n=o=>{e.value&&Wl(o,t.value)&&!ql(o.target)&&(o.preventDefault(),e.value.focus())};J(()=>{document.addEventListener("keydown",n)}),nt(()=>{document.removeEventListener("keydown",n)})},Jl=[{title:"",headers:[{level:2,title:"预览",slug:"预览",link:"#预览",children:[]},{level:2,title:"特色",slug:"特色",link:"#特色",children:[]},{level:2,title:"交流群",slug:"交流群",link:"#交流群",children:[]},{level:2,title:"历程",slug:"历程",link:"#历程",children:[]}],path:"/",pathLocale:"/",extraFields:[]},{title:"页面配置",headers:[{level:2,title:"Front-matter 的基本认识",slug:"front-matter-的基本认识",link:"#front-matter-的基本认识",children:[]},{level:2,title:"标签页",slug:"标签页",link:"#标签页",children:[]},{level:2,title:"分类页",slug:"分类页",link:"#分类页",children:[]},{level:2,title:"首页即刻说说页面配置",slug:"首页即刻说说页面配置",link:"#首页即刻说说页面配置",children:[]},{level:2,title:"友情链接配置",slug:"友情链接配置",link:"#友情链接配置",children:[]},{level:2,title:"关于页面配置",slug:"关于页面配置",link:"#关于页面配置",children:[]},{level:2,title:"配置相册页面",slug:"配置相册页面",link:"#配置相册页面",children:[]},{level:2,title:"朋友圈页面配置",slug:"朋友圈页面配置",link:"#朋友圈页面配置",children:[]},{level:2,title:"音乐馆页配置",slug:"音乐馆页配置",link:"#音乐馆页配置",children:[]},{level:2,title:"404 页面",slug:"_404-页面",link:"#_404-页面",children:[]},{level:2,title:"追番页面",slug:"追番页面",link:"#追番页面",children:[]},{level:2,title:"留言板页面",slug:"留言板页面",link:"#留言板页面",children:[]},{level:2,title:"我的装备页面",slug:"我的装备页面",link:"#我的装备页面",children:[]}],path:"/page-configuration.html",pathLocale:"/",extraFields:[]},{title:"",headers:[{level:2,title:"💻 安裝",slug:"💻-安裝",link:"#💻-安裝",children:[]},{level:2,title:"⚙ 应用主题",slug:"⚙-应用主题",link:"#⚙-应用主题",children:[]},{level:2,title:"安装 pug 和 stylus 渲染插件",slug:"安装-pug-和-stylus-渲染插件",link:"#安装-pug-和-stylus-渲染插件",children:[]},{level:2,title:"更好的配置，便于升级主题",slug:"更好的配置-便于升级主题",link:"#更好的配置-便于升级主题",children:[]},{level:2,title:"本地启动 hexo",slug:"本地启动-hexo",link:"#本地启动-hexo",children:[]},{level:2,title:"有问题?",slug:"有问题",link:"#有问题",children:[]},{level:2,title:"群聊",slug:"群聊",link:"#群聊",children:[]},{level:2,title:"技术支持",slug:"技术支持",link:"#技术支持",children:[]},{level:2,title:"主题设计",slug:"主题设计",link:"#主题设计",children:[]}],path:"/quick-start.html",pathLocale:"/",extraFields:[]},{title:"赞赏名单",headers:[],path:"/reward-list.html",pathLocale:"/",extraFields:[]},{title:"站点基础配置(一)",headers:[{level:2,title:"语言",slug:"语言",link:"#语言",children:[]},{level:2,title:"网站资料",slug:"网站资料",link:"#网站资料",children:[]},{level:2,title:"导航配置",slug:"导航配置",link:"#导航配置",children:[]},{level:2,title:"导航栏设置",slug:"导航栏设置",link:"#导航栏设置",children:[]},{level:2,title:"代码块配置",slug:"代码块配置",link:"#代码块配置",children:[{level:3,title:"代码高亮主题",slug:"代码高亮主题",link:"#代码高亮主题",children:[]},{level:3,title:"代码复制",slug:"代码复制",link:"#代码复制",children:[]},{level:3,title:"代码框展开/关闭",slug:"代码框展开-关闭",link:"#代码框展开-关闭",children:[]},{level:3,title:"代码换行",slug:"代码换行",link:"#代码换行",children:[]},{level:3,title:"代码高度限制",slug:"代码高度限制",link:"#代码高度限制",children:[]}]},{level:2,title:"图标配置",slug:"图标配置",link:"#图标配置",children:[]},{level:2,title:"顶部图",slug:"顶部图",link:"#顶部图",children:[]},{level:2,title:"文章置顶",slug:"文章置顶",link:"#文章置顶",children:[]},{level:2,title:"文章封面",slug:"文章封面",link:"#文章封面",children:[]},{level:2,title:"文章 meta 显示",slug:"文章-meta-显示",link:"#文章-meta-显示",children:[]},{level:2,title:"文章版权",slug:"文章版权",link:"#文章版权",children:[]},{level:2,title:"文章打赏",slug:"文章打赏",link:"#文章打赏",children:[]},{level:2,title:"TOC",slug:"toc",link:"#toc",children:[]},{level:2,title:"相关文章",slug:"相关文章",link:"#相关文章",children:[]},{level:2,title:"文章过期提醒",slug:"文章过期提醒",link:"#文章过期提醒",children:[]},{level:2,title:"文章编辑按钮",slug:"文章编辑按钮",link:"#文章编辑按钮",children:[]},{level:2,title:"文章分页按钮",slug:"文章分页按钮",link:"#文章分页按钮",children:[]},{level:2,title:"中控台",slug:"中控台",link:"#中控台",children:[]}],path:"/site-configuration1.html",pathLocale:"/",extraFields:[]},{title:"站点基础配置(二)",headers:[{level:2,title:"Footer 设置",slug:"footer-设置",link:"#footer-设置",children:[]},{level:2,title:"侧边栏设置 (aside)",slug:"侧边栏设置-aside",link:"#侧边栏设置-aside",children:[]},{level:2,title:"访问人数 busuanzi (UV 和 PV)",slug:"访问人数-busuanzi-uv-和-pv",link:"#访问人数-busuanzi-uv-和-pv",children:[]},{level:2,title:"运行时间",slug:"运行时间",link:"#运行时间",children:[]},{level:2,title:"最新评论",slug:"最新评论",link:"#最新评论",children:[]},{level:2,title:"右下角按钮 (Bottom right button)",slug:"右下角按钮-bottom-right-button",link:"#右下角按钮-bottom-right-button",children:[{level:3,title:"简繁转换",slug:"简繁转换",link:"#简繁转换",children:[]},{level:3,title:"阅读模式",slug:"阅读模式",link:"#阅读模式",children:[]},{level:3,title:"夜间模式",slug:"夜间模式",link:"#夜间模式",children:[]}]},{level:2,title:"按钮排序",slug:"按钮排序",link:"#按钮排序",children:[]},{level:2,title:"标签外挂（Tag Plugins）",slug:"标签外挂-tag-plugins",link:"#标签外挂-tag-plugins",children:[]},{level:2,title:"分析统计",slug:"分析统计",link:"#分析统计",children:[]},{level:2,title:"广告",slug:"广告",link:"#广告",children:[]},{level:2,title:"页面加载动画 preloader",slug:"页面加载动画-preloader",link:"#页面加载动画-preloader",children:[]},{level:2,title:"图片大图查看模式",slug:"图片大图查看模式",link:"#图片大图查看模式",children:[]},{level:2,title:"Pjax",slug:"pjax",link:"#pjax",children:[]},{level:2,title:"Snackbar 弹窗",slug:"snackbar-弹窗",link:"#snackbar-弹窗",children:[]},{level:2,title:"Pangu",slug:"pangu",link:"#pangu",children:[]},{level:2,title:"PWA",slug:"pwa",link:"#pwa",children:[]},{level:2,title:"Open Graph",slug:"open-graph",link:"#open-graph",children:[]},{level:2,title:"CSS 前缀",slug:"css-前缀",link:"#css-前缀",children:[]},{level:2,title:"Inject",slug:"inject",link:"#inject",children:[]},{level:2,title:"CDN",slug:"cdn",link:"#cdn",children:[{level:3,title:"version",slug:"version",link:"#version",children:[]},{level:3,title:"custom_format",slug:"custom-format",link:"#custom-format",children:[]}]}],path:"/site-configuration2.html",pathLocale:"/",extraFields:[]},{title:"站点基础配置(三)",headers:[{level:2,title:"ai 摘要",slug:"ai-摘要",link:"#ai-摘要",children:[]},{level:2,title:"控制台信息",slug:"控制台信息",link:"#控制台信息",children:[]},{level:2,title:"如何配置首页顶部右侧不使用轮播图",slug:"如何配置首页顶部右侧不使用轮播图",link:"#如何配置首页顶部右侧不使用轮播图",children:[]},{level:2,title:"设置 标签卖萌",slug:"设置-标签卖萌",link:"#设置-标签卖萌",children:[]},{level:2,title:"如何配置页脚",slug:"如何配置页脚",link:"#如何配置页脚",children:[]},{level:2,title:"配置关于页与文章页底部打赏二维码",slug:"配置关于页与文章页底部打赏二维码",link:"#配置关于页与文章页底部打赏二维码",children:[]},{level:2,title:"主题如何获取文章主色调",slug:"主题如何获取文章主色调",link:"#主题如何获取文章主色调",children:[]},{level:2,title:"双栏",slug:"双栏",link:"#双栏",children:[]},{level:2,title:"首页顶部 3 大分类配置",slug:"首页顶部-3-大分类配置",link:"#首页顶部-3-大分类配置",children:[]},{level:2,title:"左下角歌单",slug:"左下角歌单",link:"#左下角歌单",children:[]},{level:2,title:"首页技能点配置",slug:"首页技能点配置",link:"#首页技能点配置",children:[]},{level:2,title:"配置 nav 顶栏左侧应用列表",slug:"配置-nav-顶栏左侧应用列表",link:"#配置-nav-顶栏左侧应用列表",children:[]},{level:2,title:"字数统计",slug:"字数统计",link:"#字数统计",children:[]},{level:2,title:"网站验证",slug:"网站验证",link:"#网站验证",children:[]}],path:"/site-configuration3.html",pathLocale:"/",extraFields:[]},{title:"站点基础配置(四)",headers:[{level:2,title:"数学公式",slug:"数学公式",link:"#数学公式",children:[]},{level:2,title:"搜索系统",slug:"搜索系统",link:"#搜索系统",children:[]},{level:2,title:"分享",slug:"分享",link:"#分享",children:[]},{level:2,title:"评论",slug:"评论",link:"#评论",children:[]},{level:2,title:"在线聊天",slug:"在线聊天",link:"#在线聊天",children:[]}],path:"/site-configuration4.html",pathLocale:"/",extraFields:[]},{title:"",headers:[],path:"/404.html",pathLocale:"/",extraFields:[]}],Ql=x(Jl),Yl=()=>Ql,Zl=({searchIndex:e,routeLocale:t,query:n,maxSuggestions:o})=>{const r=g(()=>e.value.filter(a=>a.pathLocale===t.value));return g(()=>{const a=n.value.trim().toLowerCase();if(!a)return[];const c=[],l=(i,s)=>{Bt(a,[s.title])&&c.push({link:`${i.path}#${s.slug}`,title:i.title,header:s.title});for(const u of s.children){if(c.length>=o.value)return;l(i,u)}};for(const i of r.value){if(c.length>=o.value)break;if(Bt(a,[i.title,...i.extraFields])){c.push({link:i.path,title:i.title});continue}for(const s of i.headers){if(c.length>=o.value)break;l(i,s)}}return c})},Xl=e=>{const t=x(0);return{focusIndex:t,focusNext:()=>{t.value<e.value.length-1?t.value+=1:t.value=0},focusPrev:()=>{t.value>0?t.value-=1:t.value=e.value.length-1}}},es=A({name:"SearchBox",props:{locales:{type:Object,required:!1,default:()=>({})},hotKeys:{type:Array,required:!1,default:()=>[]},maxSuggestions:{type:Number,required:!1,default:5}},setup(e){const{locales:t,hotKeys:n,maxSuggestions:o}=qe(e),r=he(),a=we(),c=Yl(),l=x(null),i=x(!1),s=x(""),u=g(()=>t.value[a.value]??{}),v=Zl({searchIndex:c,routeLocale:a,query:s,maxSuggestions:o}),{focusIndex:f,focusNext:m,focusPrev:b}=Xl(v);Gl({input:l,hotKeys:n});const k=g(()=>i.value&&!!v.value.length),_=()=>{k.value&&b()},w=()=>{k.value&&m()},N=T=>{if(!k.value)return;const F=v.value[T];F&&r.push(F.link).then(()=>{s.value="",f.value=0})};return()=>S("form",{class:"search-box",role:"search"},[S("input",{ref:l,type:"search",placeholder:u.value.placeholder,autocomplete:"off",spellcheck:!1,value:s.value,onFocus:()=>i.value=!0,onBlur:()=>i.value=!1,onInput:T=>s.value=T.target.value,onKeydown:T=>{switch(T.key){case"ArrowUp":{_();break}case"ArrowDown":{w();break}case"Enter":{T.preventDefault(),N(f.value);break}}}}),k.value&&S("ul",{class:"suggestions",onMouseleave:()=>f.value=-1},v.value.map(({link:T,title:F,header:q},Z)=>S("li",{class:["suggestion",{focus:f.value===Z}],onMouseenter:()=>f.value=Z,onMousedown:()=>N(Z)},S("a",{href:T,onClick:L=>L.preventDefault()},[S("span",{class:"page-title"},F),q&&S("span",{class:"page-header"},`> ${q}`)]))))])}});const ts={"/":{placeholder:"Search"},"/zh/":{placeholder:"搜索"}},ns=["s","/"],os=5,rs=re({enhance({app:e}){e.component("SearchBox",t=>S(es,{locales:ts,hotKeys:ns,maxSuggestions:os,...t}))}});const as={};function Tn(e){return Zt()?(Xt(e),!0):!1}function Te(e){return typeof e=="function"?e():p(e)}const ht=typeof window<"u",ls=()=>{};function ss(e,t){function n(...o){return new Promise((r,a)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(r).catch(a)})}return n}const Pn=e=>e();function is(e=Pn){const t=x(!0);function n(){t.value=!1}function o(){t.value=!0}const r=(...a)=>{t.value&&e(...a)};return{isActive:je(t),pause:n,resume:o,eventFilter:r}}function us(e,t,n={}){const{immediate:o=!0}=n,r=x(!1);let a=null;function c(){a&&(clearTimeout(a),a=null)}function l(){r.value=!1,c()}function i(...s){c(),r.value=!0,a=setTimeout(()=>{r.value=!1,a=null,e(...s)},Te(t))}return o&&(r.value=!0,ht&&i()),Tn(l),{isPending:je(r),start:i,stop:l}}var Ft=Object.getOwnPropertySymbols,cs=Object.prototype.hasOwnProperty,ds=Object.prototype.propertyIsEnumerable,vs=(e,t)=>{var n={};for(var o in e)cs.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&Ft)for(var o of Ft(e))t.indexOf(o)<0&&ds.call(e,o)&&(n[o]=e[o]);return n};function fs(e,t,n={}){const o=n,{eventFilter:r=Pn}=o,a=vs(o,["eventFilter"]);return oe(e,ss(r,t),a)}var ps=Object.defineProperty,ms=Object.defineProperties,hs=Object.getOwnPropertyDescriptors,Be=Object.getOwnPropertySymbols,xn=Object.prototype.hasOwnProperty,Cn=Object.prototype.propertyIsEnumerable,jt=(e,t,n)=>t in e?ps(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,gs=(e,t)=>{for(var n in t||(t={}))xn.call(t,n)&&jt(e,n,t[n]);if(Be)for(var n of Be(t))Cn.call(t,n)&&jt(e,n,t[n]);return e},_s=(e,t)=>ms(e,hs(t)),bs=(e,t)=>{var n={};for(var o in e)xn.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&Be)for(var o of Be(e))t.indexOf(o)<0&&Cn.call(e,o)&&(n[o]=e[o]);return n};function ys(e,t,n={}){const o=n,{eventFilter:r}=o,a=bs(o,["eventFilter"]),{eventFilter:c,pause:l,resume:i,isActive:s}=is(r);return{stop:fs(e,t,_s(gs({},a),{eventFilter:c})),pause:l,resume:i,isActive:s}}function ks(e){var t;const n=Te(e);return(t=n==null?void 0:n.$el)!=null?t:n}const tt=ht?window:void 0,ws=ht?window.navigator:void 0;function Fe(...e){let t,n,o,r;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,r]=e,t=tt):[t,n,o,r]=e,!t)return ls;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const a=[],c=()=>{a.forEach(u=>u()),a.length=0},l=(u,v,f,m)=>(u.addEventListener(v,f,m),()=>u.removeEventListener(v,f,m)),i=oe(()=>[ks(t),Te(r)],([u,v])=>{c(),u&&a.push(...n.flatMap(f=>o.map(m=>l(u,f,m,v))))},{immediate:!0,flush:"post"}),s=()=>{i(),c()};return Tn(s),s}function Ls(){const e=x(!1);return rt()&&J(()=>{e.value=!0}),e}function Ss(e){const t=Ls();return g(()=>(t.value,Boolean(e())))}function Es(e={}){const{navigator:t=ws,read:n=!1,source:o,copiedDuring:r=1500,legacy:a=!1}=e,c=["copy","cut"],l=Ss(()=>t&&"clipboard"in t),i=g(()=>l.value||a),s=x(""),u=x(!1),v=us(()=>u.value=!1,r);function f(){l.value?t.clipboard.readText().then(_=>{s.value=_}):s.value=k()}if(i.value&&n)for(const _ of c)Fe(_,f);async function m(_=Te(o)){i.value&&_!=null&&(l.value?await t.clipboard.writeText(_):b(_),s.value=_,u.value=!0,v.start())}function b(_){const w=document.createElement("textarea");w.value=_??"",w.style.position="absolute",w.style.opacity="0",document.body.appendChild(w),w.select(),document.execCommand("copy"),w.remove()}function k(){var _,w,N;return(N=(w=(_=document==null?void 0:document.getSelection)==null?void 0:_.call(document))==null?void 0:w.toString())!=null?N:""}return{isSupported:i,text:s,copied:u,copy:m}}const Ie=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Ae="__vueuse_ssr_handlers__",$s=Os();function Os(){return Ae in Ie||(Ie[Ae]=Ie[Ae]||{}),Ie[Ae]}function Ts(e,t){return $s[e]||t}function Ps(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}var xs=Object.defineProperty,Vt=Object.getOwnPropertySymbols,Cs=Object.prototype.hasOwnProperty,Is=Object.prototype.propertyIsEnumerable,qt=(e,t,n)=>t in e?xs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Wt=(e,t)=>{for(var n in t||(t={}))Cs.call(t,n)&&qt(e,n,t[n]);if(Vt)for(var n of Vt(t))Is.call(t,n)&&qt(e,n,t[n]);return e};const As={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Ut="vueuse-storage";function zs(e,t,n,o={}){var r;const{flush:a="pre",deep:c=!0,listenToStorageChanges:l=!0,writeDefaults:i=!0,mergeDefaults:s=!1,shallow:u,window:v=tt,eventFilter:f,onError:m=d=>{console.error(d)}}=o,b=(u?en:x)(t);if(!n)try{n=Ts("getDefaultStorage",()=>{var d;return(d=tt)==null?void 0:d.localStorage})()}catch(d){m(d)}if(!n)return b;const k=Te(t),_=Ps(k),w=(r=o.serializer)!=null?r:As[_],{pause:N,resume:T}=ys(b,()=>F(b.value),{flush:a,deep:c,eventFilter:f});return v&&l&&(Fe(v,"storage",L),Fe(v,Ut,Z)),L(),b;function F(d){try{if(d==null)n.removeItem(e);else{const $=w.write(d),O=n.getItem(e);O!==$&&(n.setItem(e,$),v&&v.dispatchEvent(new CustomEvent(Ut,{detail:{key:e,oldValue:O,newValue:$,storageArea:n}})))}}catch($){m($)}}function q(d){const $=d?d.newValue:n.getItem(e);if($==null)return i&&k!==null&&n.setItem(e,w.write(k)),k;if(!d&&s){const O=w.read($);return typeof s=="function"?s(O,k):_==="object"&&!Array.isArray(O)?Wt(Wt({},k),O):O}else return typeof $!="string"?$:w.read($)}function Z(d){L(d.detail)}function L(d){if(!(d&&d.storageArea!==n)){if(d&&d.key==null){b.value=k;return}if(!(d&&d.key!==e)){N();try{b.value=q(d)}catch($){m($)}finally{d?Oe(T):T()}}}}}function Ns(){const e=x(!1);return rt()&&J(()=>{e.value=!0}),e}function Ds(e){return Ns(),g(()=>!!e())}const Hs=()=>Ds(()=>typeof window<"u"&&window.navigator&&"userAgent"in window.navigator),Rs=()=>{const e=Hs();return g(()=>e.value&&/\b(?:Android|iPhone)/i.test(navigator.userAgent))},Ms=e=>{const t=we();return g(()=>e[t.value])},Bs=800,Fs=2e3,js={"/":{copy:"复制代码",copied:"已复制",hint:"复制成功"}},Vs=!1,qs=['.theme-default-content div[class*="language-"] pre'],Kt=!1,Ye=new Map,Ws=()=>{const{copy:e}=Es({legacy:!0}),t=Ms(js),n=fe(),o=Rs(),r=l=>{if(!l.hasAttribute("copy-code-registered")){const i=document.createElement("button");i.type="button",i.classList.add("copy-code-button"),i.innerHTML='<div class="copy-icon" />',i.setAttribute("aria-label",t.value.copy),i.setAttribute("data-copied",t.value.copied),l.parentElement&&l.parentElement.insertBefore(i,l),l.setAttribute("copy-code-registered","")}},a=()=>Oe().then(()=>new Promise(l=>{setTimeout(()=>{qs.forEach(i=>{document.querySelectorAll(i).forEach(r)}),l()},Bs)})),c=(l,i,s)=>{let{innerText:u=""}=i;/language-(shellscript|shell|bash|sh|zsh)/.test(l.classList.toString())&&(u=u.replace(/^ *(\$|>) /gm,"")),e(u).then(()=>{s.classList.add("copied"),clearTimeout(Ye.get(s));const v=setTimeout(()=>{s.classList.remove("copied"),s.blur(),Ye.delete(s)},Fs);Ye.set(s,v)})};J(()=>{(!o.value||Kt)&&a(),Fe("click",l=>{const i=l.target;if(i.matches('div[class*="language-"] > button.copy')){const s=i.parentElement,u=i.nextElementSibling;u&&c(s,u,i)}else if(i.matches('div[class*="language-"] div.copy-icon')){const s=i.parentElement,u=s.parentElement,v=s.nextElementSibling;v&&c(u,v,s)}}),oe(()=>n.value.path,()=>{(!o.value||Kt)&&a()})})};var Us=re({setup:()=>{Ws()}});const In=({title:e,desc:t="",logo:n="",color:o="",link:r=""})=>S("a",{class:"vp-card",href:r,target:"_blank",...o?{style:{background:o}}:{}},[S("img",{class:"vp-card-logo",src:n}),S("div",{class:"vp-card-content"},[S("div",{class:"vp-card-title",innerHTML:e}),S("hr"),S("div",{class:"vp-card-desc",innerHTML:t})])]);In.displayName="VPCard";const Ze=zs("VUEPRESS_TAB_STORE",{});var Ks=A({name:"Tabs",props:{active:{type:Number,default:0},data:{type:Array,required:!0},id:{type:String,required:!0},tabId:{type:String,default:""}},setup(e,{slots:t}){const n=x(e.active),o=x([]),r=()=>{if(e.tabId){const{title:s,id:u=s}=e.data[n.value];Ze.value[e.tabId]=u}},a=(s=n.value)=>{n.value=s<o.value.length-1?s+1:0,o.value[n.value].focus()},c=(s=n.value)=>{n.value=s>0?s-1:o.value.length-1,o.value[n.value].focus()},l=(s,u)=>{s.key===" "||s.key==="Enter"?(s.preventDefault(),n.value=u):s.key==="ArrowRight"?(s.preventDefault(),a()):s.key==="ArrowLeft"&&(s.preventDefault(),c()),r()},i=()=>{if(e.tabId){const s=e.data.findIndex(({title:u,id:v=u})=>Ze.value[e.tabId]===v);if(s!==-1)return s}return e.active};return J(()=>{n.value=i(),oe(()=>Ze.value[e.tabId],(s,u)=>{if(e.tabId&&s!==u){const v=e.data.findIndex(({title:f,id:m=f})=>m===s);v!==-1&&(n.value=v)}})}),()=>e.data.length?S("div",{class:"tab-list"},[S("div",{class:"tab-list-nav",role:"tablist"},e.data.map(({title:s},u)=>{const v=u===n.value;return S("button",{type:"button",ref:f=>{f&&(o.value[u]=f)},class:["tab-list-nav-item",{active:v}],role:"tab","aria-controls":`tab-${e.id}-${u}`,"aria-selected":v,onClick:()=>{n.value=u,r()},onKeydown:f=>l(f,u)},s)})),e.data.map(({title:s,id:u=s},v)=>{var f;const m=v===n.value;return S("div",{class:["tab-item",{active:m}],id:`tab-${e.id}-${v}`,role:"tabpanel","aria-expanded":m},(f=t[`tab${v}`])==null?void 0:f.call(t,{title:s,value:u,isActive:m}))})]):null}});const Gs=re({enhance:({app:e})=>{e.component("VPCard",In),e.component("Tabs",Ks)}}),ze=[Eo,To,Io,qo,Go,er,Vl,rs,as,Us,Gs],Js=[["v-8daa1a0e","/",{title:""},["/index.html","/README.md"]],["v-65358688","/page-configuration.html",{title:"页面配置"},["/page-configuration","/page-configuration.md"]],["v-96f5eae0","/quick-start.html",{title:""},["/quick-start","/quick-start.md"]],["v-a39f3254","/reward-list.html",{title:"赞赏名单"},["/reward-list","/reward-list.md"]],["v-5dbf1831","/site-configuration1.html",{title:"站点基础配置(一)"},["/site-configuration1","/site-configuration1.md"]],["v-5f73f0d0","/site-configuration2.html",{title:"站点基础配置(二)"},["/site-configuration2","/site-configuration2.md"]],["v-6128c96f","/site-configuration3.html",{title:"站点基础配置(三)"},["/site-configuration3","/site-configuration3.md"]],["v-62dda20e","/site-configuration4.html",{title:"站点基础配置(四)"},["/site-configuration4","/site-configuration4.md"]],["v-3706649a","/404.html",{title:""},["/404"]]];var Gt=A({name:"Vuepress",setup(){const e=po();return()=>S(e.value)}}),Qs=()=>Js.reduce((e,[t,n,o,r])=>(e.push({name:t,path:n,component:Gt,meta:o},...r.map(a=>({path:a,redirect:n}))),e),[{name:"404",path:"/:catchAll(.*)",component:Gt}]),Ys=no,Zs=()=>{const e=Xn({history:Ys(nn("/anzhiyu-docs/")),routes:Qs(),scrollBehavior:(t,n,o)=>o||(t.hash?{el:t.hash}:{top:0})});return e.beforeResolve(async(t,n)=>{var o;(t.path!==n.path||n===eo)&&([ve.value]=await Promise.all([le.resolvePageData(t.name),(o=on[t.name])==null?void 0:o.__asyncLoader()]))}),e},Xs=e=>{e.component("ClientOnly",ut),e.component("Content",bo)},ei=(e,t,n)=>{const o=x(t.currentRoute.value.path);oe(()=>t.currentRoute.value.path,f=>o.value=f);const r=g(()=>le.resolveLayouts(n)),a=g(()=>le.resolveRouteLocale(ye.value.locales,o.value)),c=g(()=>le.resolveSiteLocaleData(ye.value,a.value)),l=g(()=>le.resolvePageFrontmatter(ve.value)),i=g(()=>le.resolvePageHeadTitle(ve.value,c.value)),s=g(()=>le.resolvePageHead(i.value,l.value,c.value)),u=g(()=>le.resolvePageLang(ve.value)),v=g(()=>le.resolvePageLayout(ve.value,r.value));return e.provide(io,r),e.provide(an,l),e.provide(vo,i),e.provide(ln,s),e.provide(sn,u),e.provide(un,v),e.provide(st,a),e.provide(cn,c),Object.defineProperties(e.config.globalProperties,{$frontmatter:{get:()=>l.value},$head:{get:()=>s.value},$headTitle:{get:()=>i.value},$lang:{get:()=>u.value},$page:{get:()=>ve.value},$routeLocale:{get:()=>a.value},$site:{get:()=>ye.value},$siteLocale:{get:()=>c.value},$withBase:{get:()=>ct}}),{layouts:r,pageData:ve,pageFrontmatter:l,pageHead:s,pageHeadTitle:i,pageLang:u,pageLayout:v,routeLocale:a,siteData:ye,siteLocaleData:c}},ti=()=>{const e=co(),t=fo(),n=x([]),o=()=>{e.value.forEach(a=>{const c=ni(a);c&&n.value.push(c)})},r=()=>{document.documentElement.lang=t.value,n.value.forEach(a=>{a.parentNode===document.head&&document.head.removeChild(a)}),n.value.splice(0,n.value.length),e.value.forEach(a=>{const c=oi(a);c!==null&&(document.head.appendChild(c),n.value.push(c))})};at(ho,r),J(()=>{o(),r(),oe(()=>e.value,()=>r())})},ni=([e,t,n=""])=>{const o=Object.entries(t).map(([l,i])=>te(i)?`[${l}=${JSON.stringify(i)}]`:i===!0?`[${l}]`:"").join(""),r=`head > ${e}${o}`;return Array.from(document.querySelectorAll(r)).find(l=>l.innerText===n)||null},oi=([e,t,n])=>{if(!te(e))return null;const o=document.createElement(e);return lt(t)&&Object.entries(t).forEach(([r,a])=>{te(a)?o.setAttribute(r,a):a===!0&&o.setAttribute(r,"")}),te(n)&&o.appendChild(document.createTextNode(n)),o},ri=to,ai=async()=>{var n;const e=ri({name:"VuepressApp",setup(){var o;ti();for(const r of ze)(o=r.setup)==null||o.call(r);return()=>[S(oo),...ze.flatMap(({rootComponents:r=[]})=>r.map(a=>S(a)))]}}),t=Zs();Xs(e),ei(e,t,ze);for(const o of ze)await((n=o.enhance)==null?void 0:n.call(o,{app:e,router:t,siteData:ye}));return e.use(t),{app:e,router:t}};ai().then(({app:e,router:t})=>{t.isReady().then(()=>{e.mount("#app")})});export{ai as createVueApp};
