#!/usr/bin/env python3
"""
GitHub Actions配置生成器
用于在GitHub Actions环境中动态生成配置文件
"""

import os
import yaml
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_config():
    """从环境变量生成配置文件"""

    # 检查必要的环境变量 - 支持bot token模式
    # 每个账户需要API ID/Hash，然后要么有session要么有bot token
    base_required_vars = [
        'TELEGRAM_API_ID_1', 'TELEGRAM_API_HASH_1',
        'TELEGRAM_API_ID_2', 'TELEGRAM_API_HASH_2'
    ]

    missing_vars = []
    for var in base_required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"缺少必要的环境变量: {missing_vars}")
        logger.info("请在GitHub仓库的Settings -> Secrets and variables -> Actions中设置这些变量")
        return False

    # 检查每个账户是否有session或bot token
    accounts_config = []
    for i in [1, 2]:
        session_var = f'TELEGRAM_SESSION_{i}'
        bot_token_var = f'TELEGRAM_BOT_TOKEN{i}'

        session = os.environ.get(session_var)
        bot_token = os.environ.get(bot_token_var)

        if not session and not bot_token:
            logger.error(f"账户{i}缺少认证信息：需要设置 {session_var} 或 {bot_token_var}")
            missing_vars.append(f"{session_var} 或 {bot_token_var}")

    if missing_vars:
        logger.error("请为每个账户设置session数据或bot token")
        return False
    
    # 生成配置
    accounts = []
    for i in [1, 2]:
        account_config = {
            'name': f'account{i}',
            'telegram': {
                'api_id': os.environ.get(f'TELEGRAM_API_ID_{i}'),
                'api_hash': os.environ.get(f'TELEGRAM_API_HASH_{i}'),
                'bot_username': 'HAX Bot'
            },
            'woiden': {
                'vps_id': ''
            },
            'nopecha': {
                'extension_path': './chrome_extensions/nopecha',
                'api_key': os.environ.get('NOPECHA_API_KEY', '')
            }
        }

        # 优先使用bot token，如果没有则使用session
        bot_token = os.environ.get(f'TELEGRAM_BOT_TOKEN{i}')
        session = os.environ.get(f'TELEGRAM_SESSION_{i}')

        if bot_token:
            account_config['telegram']['bot_token'] = bot_token
            logger.info(f"账户{i}: 使用Bot Token")
        elif session:
            account_config['telegram']['session'] = session
            logger.info(f"账户{i}: 使用Session数据")

        accounts.append(account_config)

    config = {
        'accounts': accounts,
        'global': {
            'renew_interval_days': 2,
            'check_interval_hours': 1,
            'headless': True,  # GitHub Actions必须使用无头模式
            'browser_type': 'chrome',
            'chrome_args': '--no-sandbox --disable-dev-shm-usage --disable-gpu --disable-extensions',
            'max_retries': 3,
            'screenshots': True,
            'debug': True
        }
    }
    
    # 保存配置文件
    try:
        with open('config.yaml', 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info("配置文件生成成功: config.yaml")
        
        # 验证配置
        logger.info("验证配置...")
        for account in config['accounts']:
            name = account['name']
            api_id = account['telegram']['api_id']
            api_hash = account['telegram']['api_hash']
            session = account['telegram'].get('session', '')
            bot_token = account['telegram'].get('bot_token', '')

            logger.info(f"账户 {name}:")
            logger.info(f"  API ID: {'已设置' if api_id else '未设置'}")
            logger.info(f"  API Hash: {'已设置' if api_hash else '未设置'}")
            if bot_token:
                logger.info(f"  Bot Token: 已设置")
            elif session:
                logger.info(f"  Session: 已设置")
            else:
                logger.info(f"  认证: 未设置")
        
        return True
        
    except Exception as e:
        logger.error(f"生成配置文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始生成GitHub Actions配置文件...")
    
    if generate_config():
        logger.info("配置文件生成完成")
        return 0
    else:
        logger.error("配置文件生成失败")
        return 1

if __name__ == "__main__":
    exit(main())
