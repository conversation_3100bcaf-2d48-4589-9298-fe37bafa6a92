#!/usr/bin/env python3
"""
GitHub Actions配置生成器
用于在GitHub Actions环境中动态生成配置文件
"""

import os
import yaml
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_config():
    """从环境变量生成配置文件"""
    
    # 检查必要的环境变量
    required_vars = [
        'TELEGRAM_API_ID_1', 'TELEGRAM_API_HASH_1', 'TELEGRAM_SESSION_1',
        'TELEGRAM_API_ID_2', 'TELEGRAM_API_HASH_2', 'TELEGRAM_SESSION_2'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {missing_vars}")
        logger.info("请在GitHub仓库的Settings -> Secrets and variables -> Actions中设置这些变量")
        return False
    
    # 生成配置
    config = {
        'accounts': [
            {
                'name': 'account1',
                'telegram': {
                    'api_id': os.environ.get('TELEGRAM_API_ID_1'),
                    'api_hash': os.environ.get('TELEGRAM_API_HASH_1'),
                    'session': os.environ.get('TELEGRAM_SESSION_1'),
                    'bot_username': 'HAX Bot'
                },
                'woiden': {
                    'vps_id': ''
                },
                'nopecha': {
                    'extension_path': './chrome_extensions/nopecha',
                    'api_key': os.environ.get('NOPECHA_API_KEY', '')
                }
            },
            {
                'name': 'account2',
                'telegram': {
                    'api_id': os.environ.get('TELEGRAM_API_ID_2'),
                    'api_hash': os.environ.get('TELEGRAM_API_HASH_2'),
                    'session': os.environ.get('TELEGRAM_SESSION_2'),
                    'bot_username': 'HAX Bot'
                },
                'woiden': {
                    'vps_id': ''
                },
                'nopecha': {
                    'extension_path': './chrome_extensions/nopecha',
                    'api_key': os.environ.get('NOPECHA_API_KEY', '')
                }
            }
        ],
        'global': {
            'renew_interval_days': 2,
            'check_interval_hours': 1,
            'headless': True,  # GitHub Actions必须使用无头模式
            'browser_type': 'chrome',
            'chrome_args': '--no-sandbox --disable-dev-shm-usage --disable-gpu --disable-extensions',
            'max_retries': 3,
            'screenshots': True,
            'debug': True
        }
    }
    
    # 保存配置文件
    try:
        with open('config.yaml', 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        logger.info("配置文件生成成功: config.yaml")
        
        # 验证配置
        logger.info("验证配置...")
        for i, account in enumerate(config['accounts'], 1):
            name = account['name']
            api_id = account['telegram']['api_id']
            api_hash = account['telegram']['api_hash']
            session = account['telegram']['session']
            
            logger.info(f"账户 {name}:")
            logger.info(f"  API ID: {'已设置' if api_id else '未设置'}")
            logger.info(f"  API Hash: {'已设置' if api_hash else '未设置'}")
            logger.info(f"  Session: {'已设置' if session else '未设置'}")
        
        return True
        
    except Exception as e:
        logger.error(f"生成配置文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始生成GitHub Actions配置文件...")
    
    if generate_config():
        logger.info("配置文件生成完成")
        return 0
    else:
        logger.error("配置文件生成失败")
        return 1

if __name__ == "__main__":
    exit(main())
