---
title: 文章统计
date: 2023-05-20 22:06:17

comments: true
aside: false
top_img: false
---

<img src="https://ghchart.rshah.org/409ba5/geekswg" alt="geekswg's Blue Github Chart" />

[![geekswg](https://cdn.jsdelivr.net/gh/geekswg/geekswg/profile-snake-contrib/github-contribution-grid-snake.svg "geekswg's Github")](https://github.com/geekswg)

<!--
https://blog.eurkon.com/post/1213ef82.html?highlight=%E7%BB%9F%E8%AE%A1
当然也可以在其他页面引入文章统计图，如果出现图表显示不全的现象可以修改 div 的 height 属性。。
posts-chart 的 data-start="2021-01" 属性表示文章发布时间统计图仅显示 2021-01 及以后的文章数据。
tags-chart 的 data-length="10" 属性表示仅显示排名前 10 的标签。
categories-chart 的 data-parent="true" 属性表示 有子分类 时以旭日图显示分类，其他 无子分类 或 设置为false 或 不设置该属性 或 设置为其他非true属性 情况都以饼状图显示分类。
-->
<!-- 文章发布时间统计图 -->
<div id="posts-chart" data-start="2021-01" style="border-radius: 8px; height: 300px; padding: 10px;"></div>
<!-- 文章标签统计图 -->
<div id="tags-chart" data-length="10" style="border-radius: 8px; height: 300px; padding: 10px;"></div>
<!-- 文章分类统计图 -->
<div id="categories-chart" data-parent="true" style="border-radius: 8px; height: 300px; padding: 10px;"></div>

