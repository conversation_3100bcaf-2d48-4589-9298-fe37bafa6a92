import{_ as h,M as i,p as u,q as p,R as e,t as a,N as n,V as t,a1 as l}from"./framework-2fd1fcd7.js";const m={},g={class:"hint-container warning"},b=e("p",{class:"hint-container-title"},"警告",-1),_={href:"https://github.com/anzhiyu-c/hexo-theme-anzhiyu/releases/tag/1.4.0",target:"_blank",rel:"noopener noreferrer"},v=e("h2",{id:"💻-安裝",tabindex:"-1"},[e("a",{class:"header-anchor",href:"#💻-安裝","aria-hidden":"true"},"#"),a(" 💻 安裝")],-1),x=e("p",null,"在博客根目录里安装最新版【推荐】",-1),f=e("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[e("pre",{class:"language-bash"},[e("code",null,[e("span",{class:"token function"},"git"),a(" clone "),e("span",{class:"token parameter variable"},"-b"),a(` main https://github.com/anzhiyu-c/hexo-theme-anzhiyu.git themes/anzhiyu
`)])]),e("div",{class:"line-numbers","aria-hidden":"true"},[e("div",{class:"line-number"})])],-1),y=e("p",null,[e("strong",null,"测试版")],-1),k=e("blockquote",null,[e("p",null,"测试版可能存在 bug，追求稳定的请安装稳定版")],-1),z=e("p",null,"如果想要安装比较新的 dev 分支，可以",-1),w=e("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[e("pre",{class:"language-bash"},[e("code",null,[e("span",{class:"token function"},"git"),a(" clone "),e("span",{class:"token parameter variable"},"-b"),a(` dev https://github.com/anzhiyu-c/hexo-theme-anzhiyu.git themes/anzhiyu
`)])]),e("div",{class:"line-numbers","aria-hidden":"true"},[e("div",{class:"line-number"})])],-1),V=e("p",null,[e("strong",null,"升级方法"),a("：在主题目录下，运行")],-1),q=e("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[e("pre",{class:"language-bash"},[e("code",null,[e("span",{class:"token function"},"git"),a(` pull
`)])]),e("div",{class:"line-numbers","aria-hidden":"true"},[e("div",{class:"line-number"})])],-1),N=e("p",null,[a("或者删除"),e("strong",null,"theme/anzhiyu"),a("文件夹，然后重新安装即可。")],-1),B=e("p",null,[a("此方法只支持 Hexo 5.0.0 以上版本 "),e("strong",null,"通过 npm 安装并不会在 themes 里生成主题文件夹，而是在 node_modules 里生成")],-1),A=e("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[e("pre",{class:"language-bash"},[e("code",null,[e("span",{class:"token function"},"npm"),a(` i hexo-theme-anzhiyu
`)])]),e("div",{class:"line-numbers","aria-hidden":"true"},[e("div",{class:"line-number"})])],-1),E=e("blockquote",null,[e("p",null,"升级方法：在 Hexo 根目录下，运行")],-1),H=e("div",{class:"language-bash line-numbers-mode","data-ext":"sh"},[e("pre",{class:"language-bash"},[e("code",null,[e("span",{class:"token function"},"npm"),a(` update hexo-theme-anzhiyu
`)])]),e("div",{class:"line-numbers","aria-hidden":"true"},[e("div",{class:"line-number"})])],-1),T={href:"https://www.bilibili.com/video/BV1Rs4y127hu/?spm_id_from=333.788&vd_source=4d9717102296e4b7a60ecdfad55ae2dd",target:"_blank",rel:"noopener noreferrer"},C=l(`<h2 id="⚙-应用主题" tabindex="-1"><a class="header-anchor" href="#⚙-应用主题" aria-hidden="true">#</a> ⚙ 应用主题</h2><p>修改 hexo 配置文件<code>_config.yml</code>，把主题改为<code>anzhiyu</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">theme</span><span class="token punctuation">:</span> anzhiyu
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><h2 id="安装-pug-和-stylus-渲染插件" tabindex="-1"><a class="header-anchor" href="#安装-pug-和-stylus-渲染插件" aria-hidden="true">#</a> 安装 pug 和 stylus 渲染插件</h2><div class="language-powershell line-numbers-mode" data-ext="powershell"><pre class="language-powershell"><code>npm install hexo-renderer-pug hexo-renderer-stylus <span class="token operator">--</span>save
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><h2 id="更好的配置-便于升级主题" tabindex="-1"><a class="header-anchor" href="#更好的配置-便于升级主题" aria-hidden="true">#</a> 更好的配置，便于升级主题</h2><ul><li><p>macos/linux 在博客根目录运行</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">cp</span> <span class="token parameter variable">-rf</span> ./themes/anzhiyu/_config.yml ./_config.anzhiyu.yml
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>windows 复制<code>/themes/anzhiyu/_config.yml</code>此文件到 hexo 根目录，并重命名为<code>_config.anzhiyu.yml</code></p></li></ul><p><code>_config.anzhiyu.yml</code>中的配置优先级大于<code>_config.yml</code></p><h2 id="本地启动-hexo" tabindex="-1"><a class="header-anchor" href="#本地启动-hexo" aria-hidden="true">#</a> 本地启动 hexo</h2><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo cl
hexo g
hexo s
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>此时就能在看到效果了。</p><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/03/31/642677a150e9d.png" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><h2 id="有问题" tabindex="-1"><a class="header-anchor" href="#有问题" aria-hidden="true">#</a> 有问题?</h2>`,13),I={href:"https://github.com/anzhiyu-c/hexo-theme-anzhiyu/issues",target:"_blank",rel:"noopener noreferrer"},L=l('<h2 id="群聊" tabindex="-1"><a class="header-anchor" href="#群聊" aria-hidden="true">#</a> 群聊</h2><p>群号：<code>464636182</code></p><div><img height="300" alt="交流群464636182" src="https://img02.anheyu.com/adminuploads/1/2023/04/14/6438b945e1834.webp"></div><h2 id="技术支持" tabindex="-1"><a class="header-anchor" href="#技术支持" aria-hidden="true">#</a> 技术支持</h2><p>联系 QQ<code>2268025923</code>或群聊<code>464636182</code>内咨询</p><h2 id="主题设计" tabindex="-1"><a class="header-anchor" href="#主题设计" aria-hidden="true">#</a> 主题设计</h2>',6),Q={href:"https://github.com/zhheo",target:"_blank",rel:"noopener noreferrer"};function R(S,M){const s=i("ExternalLinkIcon"),d=i("Tabs");return u(),p("div",null,[e("div",g,[b,e("p",null,[a("本教程更新于 2023 年 7 月 5 日，教程的内容针对最新的 anzhiyu 主题(如果你是旧版本，教程会有出入，请留意) 🐟 安知鱼 已经更新到 "),e("a",_,[a("1.4.0"),n(s)])])]),v,n(d,{id:"8",data:[{title:"github 安装"},{title:"npm 安装"}],active:0},{tab0:t(({title:o,value:r,isActive:c})=>[x,f,y,k,z,w,V,q,N]),tab1:t(({title:o,value:r,isActive:c})=>[B,A,E,H]),_:1}),e("p",null,[a("什么？你还是不会？这里有视频 "),e("a",T,[a("AnZhiYu 主题安装教程"),n(s)])]),C,e("p",null,[a("可到仓库提交 "),e("a",I,[a("issues"),n(s)])]),L,e("p",null,[e("a",Q,[a("@张洪 Heo"),n(s)])])])}const Z=h(m,[["render",R],["__file","quick-start.html.vue"]]);export{Z as default};
