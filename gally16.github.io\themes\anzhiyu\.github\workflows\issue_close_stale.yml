name: Close inactive

on:
  schedule:
    - cron: "0 0 */7 * *"
  workflow_dispatch:

jobs:
  close-inactive:
    runs-on: ubuntu-latest
    steps:
      - name: close-issues
        uses: actions-cool/issues-helper@v3
        with:
          actions: "close-issues"
          token: ${{ secrets.GITHUB_TOKEN }}
          labels: "stale"
          inactive-day: 60
          close-reason: "not_planned"
          body: |
            Hello @${{ github.event.issue.user.login }}, this issue was closed due to inactive more than 60 days. You can reopen or recreate it if you think it should continue. Thank you for your contributions again.
