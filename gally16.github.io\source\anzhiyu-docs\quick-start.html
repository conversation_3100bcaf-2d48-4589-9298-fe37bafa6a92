<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.61">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <link rel="icon" href="/docs/images/c192.png"><title>安知鱼主题指南</title><meta name="description" content="anzhiyu,hexo主题,anzhiyu主题,安知鱼,博客,魔改,简单的hexo主题,简洁的hexo主题,hexo">
    <link rel="preload" href="/anzhiyu-docs/assets/style-47b50212.css" as="style"><link rel="stylesheet" href="/anzhiyu-docs/assets/style-47b50212.css">
    <link rel="modulepreload" href="/anzhiyu-docs/assets/app-18744df2.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/framework-2fd1fcd7.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/quick-start.html-5dcde832.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/quick-start.html-ad03cacb.js"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-e7463a10.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/page-configuration.html-cc2f4bd1.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-0505f6c4.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration1.html-50f88905.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-d3638ef1.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-7a1b5d87.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration4.html-e8a4bf32.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-f9875e7b.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-f69e6992.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/page-configuration.html-89a1d5ab.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-2a772ecf.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration1.html-f7afdcb5.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-e0d0931a.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-74290865.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration4.html-919542bb.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-bbf580d2.js" as="script">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container"><!--[--><header class="navbar"><div class="toggle-sidebar-button" title="toggle sidebar" aria-expanded="false" role="button" tabindex="0"><div class="icon" aria-hidden="true"><span></span><span></span><span></span></div></div><span><a href="/anzhiyu-docs/" class=""><img class="logo" src="/anzhiyu-docs/./images/c192.png" alt="安知鱼主题指南"><span class="site-name can-hide">安知鱼主题指南</span></a></span><div class="navbar-items-wrapper" style=""><!--[--><!--]--><nav class="navbar-items can-hide"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><button class="toggle-color-mode-button" title="toggle color mode"><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button><form class="search-box" role="search"><input type="search" placeholder="Search" autocomplete="off" spellcheck="false" value><!----></form></div></header><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar"><nav class="navbar-items"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><ul class="sidebar-items"><!--[--><li><a href="/anzhiyu-docs/" class="sidebar-item sidebar-heading" aria-label="简介"><!--[--><!--]--> 简介 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html" class="router-link-active router-link-exact-active router-link-active sidebar-item sidebar-heading active" aria-label="快速上手"><!--[--><!--]--> 快速上手 <!--[--><!--]--></a><ul style="" class="sidebar-item-children"><!--[--><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#💻-安裝" class="router-link-active router-link-exact-active sidebar-item" aria-label="💻 安裝"><!--[--><!--]--> 💻 安裝 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#⚙-应用主题" class="router-link-active router-link-exact-active sidebar-item" aria-label="⚙ 应用主题"><!--[--><!--]--> ⚙ 应用主题 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#安装-pug-和-stylus-渲染插件" class="router-link-active router-link-exact-active sidebar-item" aria-label="安装 pug 和 stylus 渲染插件"><!--[--><!--]--> 安装 pug 和 stylus 渲染插件 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#更好的配置-便于升级主题" class="router-link-active router-link-exact-active sidebar-item" aria-label="更好的配置，便于升级主题"><!--[--><!--]--> 更好的配置，便于升级主题 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#本地启动-hexo" class="router-link-active router-link-exact-active sidebar-item" aria-label="本地启动 hexo"><!--[--><!--]--> 本地启动 hexo <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#有问题" class="router-link-active router-link-exact-active sidebar-item" aria-label="有问题?"><!--[--><!--]--> 有问题? <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#群聊" class="router-link-active router-link-exact-active sidebar-item" aria-label="群聊"><!--[--><!--]--> 群聊 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#技术支持" class="router-link-active router-link-exact-active sidebar-item" aria-label="技术支持"><!--[--><!--]--> 技术支持 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/quick-start.html#主题设计" class="router-link-active router-link-exact-active sidebar-item" aria-label="主题设计"><!--[--><!--]--> 主题设计 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/anzhiyu-docs/page-configuration.html" class="sidebar-item sidebar-heading" aria-label="页面配置"><!--[--><!--]--> 页面配置 <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration1.html" class="sidebar-item sidebar-heading" aria-label="站点配置(一)"><!--[--><!--]--> 站点配置(一) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration2.html" class="sidebar-item sidebar-heading" aria-label="站点配置(二)"><!--[--><!--]--> 站点配置(二) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration3.html" class="sidebar-item sidebar-heading" aria-label="站点配置(三)"><!--[--><!--]--> 站点配置(三) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration4.html" class="sidebar-item sidebar-heading" aria-label="站点配置(四)"><!--[--><!--]--> 站点配置(四) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/reward-list.html" class="sidebar-item sidebar-heading" aria-label="赞赏名单"><!--[--><!--]--> 赞赏名单 <!--[--><!--]--></a><!----></li><!--]--></ul><!--[--><!--]--></aside><!--]--><!--[--><main class="page"><!--[--><!--]--><div class="theme-default-content"><!--[--><!--]--><div><div class="hint-container warning"><p class="hint-container-title">警告</p><p>本教程更新于 2023 年 7 月 5 日，教程的内容针对最新的 anzhiyu 主题(如果你是旧版本，教程会有出入，请留意) 🐟 安知鱼 已经更新到 <a href="https://github.com/anzhiyu-c/hexo-theme-anzhiyu/releases/tag/1.4.0" target="_blank" rel="noopener noreferrer">1.4.0<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><h2 id="💻-安裝" tabindex="-1"><a class="header-anchor" href="#💻-安裝" aria-hidden="true">#</a> 💻 安裝</h2><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-8-0" aria-selected="true">github 安装</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-8-1" aria-selected="false">npm 安装</button></div><!--[--><div class="tab-item active" id="tab-8-0" role="tabpanel" aria-expanded="true"><p>在博客根目录里安装最新版【推荐】</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">git</span> clone <span class="token parameter variable">-b</span> main https://github.com/anzhiyu-c/hexo-theme-anzhiyu.git themes/anzhiyu
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p><strong>测试版</strong></p><blockquote><p>测试版可能存在 bug，追求稳定的请安装稳定版</p></blockquote><p>如果想要安装比较新的 dev 分支，可以</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">git</span> clone <span class="token parameter variable">-b</span> dev https://github.com/anzhiyu-c/hexo-theme-anzhiyu.git themes/anzhiyu
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p><strong>升级方法</strong>：在主题目录下，运行</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">git</span> pull
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>或者删除<strong>theme/anzhiyu</strong>文件夹，然后重新安装即可。</p></div><div class="tab-item" id="tab-8-1" role="tabpanel" aria-expanded="false"><p>此方法只支持 Hexo 5.0.0 以上版本 <strong>通过 npm 安装并不会在 themes 里生成主题文件夹，而是在 node_modules 里生成</strong></p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">npm</span> i hexo-theme-anzhiyu
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><blockquote><p>升级方法：在 Hexo 根目录下，运行</p></blockquote><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">npm</span> update hexo-theme-anzhiyu
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></div><!--]--></div><p>什么？你还是不会？这里有视频 <a href="https://www.bilibili.com/video/BV1Rs4y127hu/?spm_id_from=333.788&amp;vd_source=4d9717102296e4b7a60ecdfad55ae2dd" target="_blank" rel="noopener noreferrer">AnZhiYu 主题安装教程<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><h2 id="⚙-应用主题" tabindex="-1"><a class="header-anchor" href="#⚙-应用主题" aria-hidden="true">#</a> ⚙ 应用主题</h2><p>修改 hexo 配置文件<code>_config.yml</code>，把主题改为<code>anzhiyu</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">theme</span><span class="token punctuation">:</span> anzhiyu
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><h2 id="安装-pug-和-stylus-渲染插件" tabindex="-1"><a class="header-anchor" href="#安装-pug-和-stylus-渲染插件" aria-hidden="true">#</a> 安装 pug 和 stylus 渲染插件</h2><div class="language-powershell line-numbers-mode" data-ext="powershell"><pre class="language-powershell"><code>npm install hexo-renderer-pug hexo-renderer-stylus <span class="token operator">--</span>save
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><h2 id="更好的配置-便于升级主题" tabindex="-1"><a class="header-anchor" href="#更好的配置-便于升级主题" aria-hidden="true">#</a> 更好的配置，便于升级主题</h2><ul><li><p>macos/linux 在博客根目录运行</p><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code><span class="token function">cp</span> <span class="token parameter variable">-rf</span> ./themes/anzhiyu/_config.yml ./_config.anzhiyu.yml
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div></li><li><p>windows 复制<code>/themes/anzhiyu/_config.yml</code>此文件到 hexo 根目录，并重命名为<code>_config.anzhiyu.yml</code></p></li></ul><p><code>_config.anzhiyu.yml</code>中的配置优先级大于<code>_config.yml</code></p><h2 id="本地启动-hexo" tabindex="-1"><a class="header-anchor" href="#本地启动-hexo" aria-hidden="true">#</a> 本地启动 hexo</h2><div class="language-bash line-numbers-mode" data-ext="sh"><pre class="language-bash"><code>hexo cl
hexo g
hexo s
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>此时就能在看到效果了。</p><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/03/31/642677a150e9d.png" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><h2 id="有问题" tabindex="-1"><a class="header-anchor" href="#有问题" aria-hidden="true">#</a> 有问题?</h2><p>可到仓库提交 <a href="https://github.com/anzhiyu-c/hexo-theme-anzhiyu/issues" target="_blank" rel="noopener noreferrer">issues<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><h2 id="群聊" tabindex="-1"><a class="header-anchor" href="#群聊" aria-hidden="true">#</a> 群聊</h2><p>群号：<code>464636182</code></p><div><img height="300" alt="交流群464636182" src="https://img02.anheyu.com/adminuploads/1/2023/04/14/6438b945e1834.webp"></div><h2 id="技术支持" tabindex="-1"><a class="header-anchor" href="#技术支持" aria-hidden="true">#</a> 技术支持</h2><p>联系 QQ<code>2268025923</code>或群聊<code>464636182</code>内咨询</p><h2 id="主题设计" tabindex="-1"><a class="header-anchor" href="#主题设计" aria-hidden="true">#</a> 主题设计</h2><p><a href="https://github.com/zhheo" target="_blank" rel="noopener noreferrer">@张洪 Heo<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><!--[--><!--]--></div><footer class="page-meta"><!----><div class="meta-item last-updated"><span class="meta-item-label">上次更新: </span><!----></div><div class="meta-item contributors"><span class="meta-item-label">贡献者: </span><span class="meta-item-info"><!--[--><!--[--><span class="contributor" title="email: <EMAIL>">anzhiyu</span><!--[-->, <!--]--><!--]--><!--[--><span class="contributor" title="email: <EMAIL>">Norris Scamander</span><!----><!--]--><!--]--></span></div></footer><nav class="page-nav"><p class="inner"><span class="prev"><a href="/anzhiyu-docs/" class="" aria-label="简介"><!--[--><!--]--> 简介 <!--[--><!--]--></a></span><span class="next"><a href="/anzhiyu-docs/page-configuration.html" class="" aria-label="页面配置"><!--[--><!--]--> 页面配置 <!--[--><!--]--></a></span></p></nav><!--[--><!--]--></main><!--]--></div><!----><!--]--></div>
    <script type="module" src="/anzhiyu-docs/assets/app-18744df2.js" defer></script>
  </body>
</html>
