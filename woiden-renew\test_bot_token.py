#!/usr/bin/env python3
"""
Bot Token测试脚本
用于验证Telegram Bot Token是否有效
"""

import asyncio
import logging
from telethon import TelegramClient

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_bot_token(api_id, api_hash, bot_token, account_name="test"):
    """测试Bot Token是否有效"""
    
    logger.info(f"测试账户 {account_name} 的Bot Token...")
    
    session_file = f"sessions/{account_name}_bot_test.session"
    
    try:
        # 创建Telegram客户端
        client = TelegramClient(session_file, api_id, api_hash)
        
        # 使用Bot Token启动
        await client.start(bot_token=bot_token)
        
        # 获取机器人信息
        me = await client.get_me()
        
        logger.info(f"✅ Bot Token有效!")
        logger.info(f"   机器人用户名: @{me.username}")
        logger.info(f"   机器人名称: {me.first_name}")
        logger.info(f"   机器人ID: {me.id}")
        
        # 关闭连接
        await client.disconnect()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Bot Token测试失败: {str(e)}")
        return False

async def main():
    """主函数"""
    logger.info("Telegram Bot Token 测试工具")
    logger.info("=" * 50)
    
    # 获取用户输入
    try:
        api_id = input("请输入 API ID: ").strip()
        api_hash = input("请输入 API Hash: ").strip()
        bot_token = input("请输入 Bot Token: ").strip()
        
        if not api_id or not api_hash or not bot_token:
            logger.error("所有字段都不能为空")
            return 1
        
        # 验证API ID是数字
        try:
            api_id = int(api_id)
        except ValueError:
            logger.error("API ID必须是数字")
            return 1
        
        # 测试Bot Token
        success = await test_bot_token(api_id, api_hash, bot_token)
        
        if success:
            logger.info("\n🎉 Bot Token测试成功！")
            logger.info("你可以在GitHub Actions中使用这个Bot Token")
            return 0
        else:
            logger.error("\n❌ Bot Token测试失败")
            logger.info("请检查:")
            logger.info("1. API ID 和 API Hash 是否正确")
            logger.info("2. Bot Token 是否有效")
            logger.info("3. 网络连接是否正常")
            return 1
            
    except KeyboardInterrupt:
        logger.info("\n用户中断测试")
        return 1
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
