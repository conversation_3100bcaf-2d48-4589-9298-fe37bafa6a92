if theme.friends_vue.enable
  .fcircle_page
    .author-content.author-content-item.fcirclePage.single(style = `background: url(${theme.friends_vue.top_background}) left 28% / cover no-repeat !important;`)
      .card-content
        .author-content-item-tips 友链
        span.author-content-item-title 最新文章订阅
        .content-bottom
          .tips=theme.friends_vue.top_tips
        .banner-button-group
          a.banner-button(onclick='pjax.loadUrl("/about")')
            i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 22px; margin-right: 0.25rem')
            span.banner-button-text 关于本人
    .title-h2-a
      .title-h2-a-left
        h2(style='padding-top:0;margin:.6rem 0 .6rem') 🎣 钓鱼
        a.random-post-start(href='javascript:fetchRandomPost();')
          i.anzhiyufont.anzhiyu-icon-arrow-rotate-right
      .title-h2-a-right
        a.random-post-all(href='/link/') 全部友链
    #random-post
    #hexo-circle-of-friends-root

  if (theme.friends_vue.apiurl)
    script(defer data-pjax src=url_for(theme.asset.random_friends_post_js))
  script(defer data-pjax src=url_for(theme.friends_vue.vue_js))
