---
title: Markdown在线编辑器
date: 2023-07-18 16:33:24
type: page
aside: false
---

<style type="text/css">
  .autoHDiv {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
  }
.autoHDiv iframe,
.autoHDiv object,
.autoHDiv embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
</style>
<p class="autoHDiv">

  <!-- https://geeks-fork.github.io/editor.md/examples/full.html -->
  <!-- https://pandao.github.io/editor.md/examples/full.html -->
  <iframe id="iFrame" name="iframe1" class="flexiframe" src="https://pandao.github.io/editor.md/examples/full.html" style="width:100%;" marginwidth="0" frameborder="no" scrolling="no" allowTransparency="true"></iframe>

</p>

<!--
<link rel="stylesheet" href="/custom/editormd/css/editormd.css" />
<div id="test-editor">
    <textarea style="display:none;">### 关于 Editor.md

**Editor.md** 是一款开源的、可嵌入的 Markdown 在线编辑器（组件），基于 CodeMirror、jQuery 和 Marked 构建。
    </textarea>
</div>

<script src="https://cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>

<script src="/custom/editormd/editormd.min.js"></script>
<script type="text/javascript">
    $(function() {
        var editor = editormd("test-editor", {
            // width  : "100%",
            // height : "500px",
            path   : "/custom/editormd/lib/"
        });
    });
</script>
-->