import{_ as r,M as o,p as h,q as i,R as e,t as a,N as n,a1 as c}from"./framework-2fd1fcd7.js";const s={},d=e("h2",{id:"预览",tabindex:"-1"},[e("a",{class:"header-anchor",href:"#预览","aria-hidden":"true"},"#"),a(" 预览")],-1),l={href:"https://gavinblog.github.io/anzhiyu-docs/",target:"_blank",rel:"noopener noreferrer"},p={href:"https://hexo.geekswg.top/",target:"_blank",rel:"noopener noreferrer"},_=c('<h2 id="特色" tabindex="-1"><a class="header-anchor" href="#特色" aria-hidden="true">#</a> 特色</h2><p>简洁而不简单</p><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/12/64367c8fdcc7f.webp" alt="安知鱼主题" tabindex="0" loading="lazy"><figcaption>安知鱼主题</figcaption></figure><h2 id="交流群" tabindex="-1"><a class="header-anchor" href="#交流群" aria-hidden="true">#</a> 交流群</h2><img height="300" alt="交流群464636182" src="https://img02.anheyu.com/adminuploads/1/2023/04/14/6438b945e1834.webp"><h2 id="历程" tabindex="-1"><a class="header-anchor" href="#历程" aria-hidden="true">#</a> 历程</h2>',6),u={href:"https://github.com/anzhiyu-c/hexo-theme-anzhiyu",target:"_blank",rel:"noopener noreferrer"};function f(m,g){const t=o("ExternalLinkIcon");return h(),i("div",null,[d,e("p",null,[e("a",l,[a("文档预览地址"),n(t)])]),e("p",null,[e("a",p,[a("博客预览地址"),n(t)])]),_,e("p",null,[e("a",u,[a("hexo-theme-anzhiyu"),n(t)]),a(" 是基于 hexo-theme-butterfly 的基础上进行开发的。")])])}const b=r(s,[["render",f],["__file","index.html.vue"]]);export{b as default};
