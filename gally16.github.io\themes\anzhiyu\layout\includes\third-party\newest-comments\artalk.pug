- const { server, site, option } = theme.artalk
- const avatarCdn = option !== null && option.gravatar && option.gravatar.mirror
- const avatarDefault = option !== null && option.gravatar && (option.gravatar.params || option.gravatar.default)

script.
  window.addEventListener('load', () => {
    const changeContent = (content) => {
      if (content === '') return content

      content = content.replace(/<img.*?src="(.*?)"?[^\>]+>/ig, '[!{_p("aside.card_newest_comments.image")}]') // replace image link
      content = content.replace(/<a[^>]+?href=["']?([^"']+)["']?[^>]*>([^<]+)<\/a>/gi, '[!{_p("aside.card_newest_comments.link")}]') // replace url
      content = content.replace(/<pre><code>.*?<\/pre>/gi, '[!{_p("aside.card_newest_comments.code")}]') // replace code
      content = content.replace(/<[^>]+>/g,"") // remove html tag

      if (content.length > 150) {
        content = content.substring(0,150) + '...'
      }
      return content
    }

    const generateHtml = array => {
      let result = ''

      if (array.length) {
        for (let i = 0; i < array.length; i++) {
          result += '<div class=\'aside-list-item\'>'

          if (!{theme.newest_comments.avatar}) {
            const name = '!{theme.lazyload.enable ? "data-lazy-src" : "src"}'
            result += `<a href='${array[i].url}' class='thumbnail'><img ${name}='${array[i].avatar}' alt='${array[i].nick}'></a>`
          }

          result += `<div class='content'>
          <a class='comment' href='${array[i].url}' title='${array[i].content}'>${array[i].content}</a>
          <div class='name'><span>${array[i].nick} / </span><time datetime="${array[i].date}">${anzhiyu.diffDate(array[i].date, true)}</time></div>
          </div></div>`
        }
      } else {
        result += '!{_p("aside.card_newest_comments.zero")}'
      }

      let $dom = document.querySelector('#card-newest-comments .aside-list')
      $dom && ($dom.innerHTML= result)
      window.lazyLoadInstance && window.lazyLoadInstance.update()
      window.pjax && window.pjax.refresh($dom)
    }

    const getSetting = async () => {
      try {
        const res = await fetch('!{server}/api/conf', { method: 'GET' })
        return await res.json()
      } catch (e) {
        console.log(e)
      }
    }

    const headerList = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': window.location.origin
      },
      body: new URLSearchParams({
        'site_name': '!{site}',
        'limit': '!{theme.newest_comments.limit}',
        'type':'latest_comments'
      })
    }

    const getComment = async () => {
      try {
        const res = await fetch('!{server}/api/stat', headerList)
        const result = await res.json()
        const avatarStr = await getSetting()
        const { mirror, params, default:defaults } = avatarStr.data.frontend_conf.gravatar
        const avatarCdn = !{avatarCdn} || mirror
        let avatarDefault = !{avatarDefault} || params || defaults
        avatarDefault = avatarDefault.startsWith('d=') ? avatarDefault : `d=${avatarDefault}`
        const artalk = result.data.map(function (e) {
          return {
            'avatar': `${avatarCdn}${e.email_encrypted}?${avatarDefault}`,
            'content': changeContent(e.content_marked),
            'nick': e.nick,
            'url': e.page_url,
            'date': e.date,
          }
        })
        saveToLocal.set('artalk-newest-comments', JSON.stringify(artalk), !{theme.newest_comments.storage}/(60*24))
        generateHtml(artalk)
      } catch (e) {
        console.log(e)
        const $dom = document.querySelector('#card-newest-comments .aside-list')
        $dom.innerHTML= "!{_p('aside.card_newest_comments.error')}"
      }
    }

    const newestCommentInit = () => {
      if (document.querySelector('#card-newest-comments .aside-list')) {
        const data = saveToLocal.get('artalk-newest-comments')
        if (data) {
          generateHtml(JSON.parse(data))
        } else {
          getComment()
        }
      }
    }

    newestCommentInit()
    document.addEventListener('pjax:complete', newestCommentInit)
  })