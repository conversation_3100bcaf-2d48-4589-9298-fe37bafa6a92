# 🚀 Woiden VPS 自动续期工具

[![GitHub Actions](https://github.com/gally16/woiden-renew/workflows/Woiden%20VPS%20多账户自动续期/badge.svg)](https://github.com/gally16/woiden-renew/actions)
[![Python](https://img.shields.io/badge/python-3.7+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

一个强大的自动化工具，帮助您轻松管理Woiden VPS的续期流程。支持多账户管理、GitHub Actions自动化运行，让您的VPS永不过期！

## ✨ 功能特性

- 🤖 **全自动化流程** - 自动访问续期页面、填写表单、处理验证码
- 🔢 **智能验证码识别** - 自动解析数学验证码和reCAPTCHA
- 📱 **Telegram集成** - 支持Bot Token和用户Session两种认证方式
- 👥 **多账户支持** - 同时管理多个Woiden VPS账户
- ☁️ **GitHub Actions** - 云端自动化运行，无需本地设备
- 🔄 **定时执行** - 自动定时续期，确保VPS不会过期
- 📸 **截图记录** - 保存操作截图，便于问题排查
- 🛡️ **安全可靠** - 支持多种认证方式，配置灵活

## 🎯 快速开始

### 方式一：GitHub Actions（推荐，零维护）

1. **Fork本仓库**
2. **设置GitHub Secrets**（详见[配置指南](#-配置指南)）
3. **启用Actions**，系统将每2天自动运行

### 方式二：本地运行

```bash
# 克隆仓库
git clone https://github.com/gally16/woiden-renew.git
cd woiden-renew

# 安装依赖
pip install -r requirements.txt

# 启动快速配置向导
python quick_start.py
```

## 📋 环境要求

- **Python** 3.7+
- **Chrome浏览器** (GitHub Actions自动安装)
- **Telegram账号** (用于接收验证码)
- **NopeCHA扩展** (可选，用于自动处理reCAPTCHA)

## ⚙️ 配置指南

### 第一步：获取Telegram API凭据

1. 访问 https://my.telegram.org
2. 使用您的手机号码登录
3. 进入 "API development tools"
4. 创建新应用，记录下：
   - `api_id` (数字)
   - `api_hash` (字符串)

### 第二步：选择认证方式

#### 方式A：Bot Token模式（推荐）

**优势**：无需交互式登录，更稳定，专为自动化设计

1. 按照 [`BOT_TOKEN_GUIDE.md`](BOT_TOKEN_GUIDE.md) 创建Telegram机器人
2. 获取Bot Token
3. 无需额外设置，直接进行第三步

#### 方式B：用户Session模式

**适用场景**：个人使用，需要交互式设置

```bash
# 安装依赖
pip install telethon

# 运行会话生成器
python generate_telegram_session.py
```

### 第三步：配置GitHub Secrets

在GitHub仓库的 `Settings` -> `Secrets and variables` -> `Actions` 中添加：

#### 必需的Secrets：

**基础配置（所有账户）:**
- `TELEGRAM_API_ID_1`: 第一个账户的API ID
- `TELEGRAM_API_HASH_1`: 第一个账户的API Hash
- `TELEGRAM_API_ID_2`: 第二个账户的API ID
- `TELEGRAM_API_HASH_2`: 第二个账户的API Hash

**认证配置（选择其一）:**

**方式A - Bot Token模式（推荐）:**
- `TELEGRAM_BOT_TOKEN1`: 第一个账户的Bot Token
- `TELEGRAM_BOT_TOKEN2`: 第二个账户的Bot Token

**方式B - 用户Session模式:**
- `TELEGRAM_SESSION_1`: 第一个账户的会话数据
- `TELEGRAM_SESSION_2`: 第二个账户的会话数据

#### 可选的Secrets：

- `NOPECHA_API_KEY`: NopeCHA API密钥（用于自动解决验证码）

### 第四步：启用GitHub Actions

1. 确保您的仓库中有 `.github/workflows/multi_vps_renew.yml` 文件
2. GitHub Actions会自动每2天运行一次
3. 您也可以在 `Actions` 标签页手动触发运行

## 🔧 本地使用

### 快速启动

```bash
# 使用交互式菜单
python quick_start.py
```

菜单选项：
1. 生成Telegram会话数据 (用户模式)
2. 测试Bot Token (机器人模式)
3. 测试配置
4. 运行单次续期
5. 查看设置指南

### 手动配置

1. 编辑 `config.yaml` 文件
2. 运行续期程序：

```bash
# 单次运行
python multi_account_renewer.py

# 定时运行
python multi_scheduler.py
```

## 🧪 测试工具

### 测试Bot Token

```bash
python test_bot_token.py
```

### 测试完整配置

```bash
python test_config.py
```

### 测试GitHub Actions配置

```bash
python test_github_actions_config.py
```

## 🔍 工作流程

1. **触发**: 每2天自动运行，或手动触发
2. **环境准备**: 安装Python依赖、Chrome浏览器等
3. **配置生成**: 从GitHub Secrets生成配置文件
4. **执行续期**:
   - 连接Telegram客户端
   - 打开Woiden续期页面
   - 自动填写表单和验证码
   - 等待Telegram验证码并自动提交
5. **结果保存**: 保存截图和日志作为artifacts

## 🚨 故障排除

### 常见错误

1. **"Please enter your phone (or bot token)"**
   - 原因：缺少Bot Token或Session数据
   - 解决：设置 `TELEGRAM_BOT_TOKEN1/2` 或 `TELEGRAM_SESSION_1/2`

2. **"API ID or Hash cannot be empty"**
   - 原因：缺少API凭据
   - 解决：检查 `TELEGRAM_API_ID_X` 和 `TELEGRAM_API_HASH_X` secrets

3. **"Bot token invalid"**
   - 原因：Bot Token无效
   - 解决：重新创建机器人或检查Token是否正确

### 查看日志

1. 进入GitHub仓库的 `Actions` 标签页
2. 点击最近的运行记录
3. 查看详细日志
4. 下载 `screenshots-and-logs` artifact 查看截图

## 📚 相关文档

- [详细设置指南](SETUP_GUIDE.md)
- [Bot Token创建指南](BOT_TOKEN_GUIDE.md)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## ⭐ 支持项目

如果这个项目对您有帮助，请给个Star⭐！