#!/usr/bin/env python3
"""
快速启动脚本
帮助用户快速设置和测试Woiden VPS自动续期系统
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 Woiden VPS 自动续期系统 - 快速启动")
    print("=" * 60)
    print()

def check_dependencies():
    """检查依赖"""
    logger.info("检查依赖包...")
    
    required_packages = {
        'selenium': 'selenium>=4.10.0',
        'telethon': 'telethon>=1.28.5', 
        'yaml': 'pyyaml>=6.0',
        'webdriver_manager': 'webdriver-manager>=3.8.6'
    }
    
    missing = []
    for package, requirement in required_packages.items():
        try:
            __import__(package)
            logger.info(f"✅ {package} 已安装")
        except ImportError:
            logger.error(f"❌ {package} 未安装")
            missing.append(requirement)
    
    if missing:
        logger.error("请先安装缺失的依赖:")
        logger.error(f"pip install {' '.join(missing)}")
        return False
    
    return True

def setup_directories():
    """创建必要的目录"""
    logger.info("创建必要的目录...")
    
    directories = ['sessions', 'screenshots', 'logs', 'chrome_extensions']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"✅ 目录 {directory} 已创建")

def show_menu():
    """显示菜单"""
    print("\n请选择操作:")
    print("1. 生成Telegram会话数据 (用户模式)")
    print("2. 测试Bot Token (机器人模式)")
    print("3. 测试配置")
    print("4. 运行单次续期")
    print("5. 查看设置指南")
    print("6. 退出")
    print()

async def run_session_generator():
    """运行会话生成器"""
    logger.info("启动Telegram会话生成器...")
    try:
        from generate_telegram_session import main as session_main
        await session_main()
    except Exception as e:
        logger.error(f"运行会话生成器失败: {str(e)}")

async def run_bot_token_test():
    """运行Bot Token测试"""
    logger.info("启动Bot Token测试...")
    try:
        from test_bot_token import main as bot_test_main
        await bot_test_main()
    except Exception as e:
        logger.error(f"运行Bot Token测试失败: {str(e)}")

async def run_config_test():
    """运行配置测试"""
    logger.info("启动配置测试...")
    try:
        from test_config import main as test_main
        await test_main()
    except Exception as e:
        logger.error(f"运行配置测试失败: {str(e)}")

async def run_renewal():
    """运行续期程序"""
    logger.info("启动续期程序...")
    
    # 检查配置文件
    if not os.path.exists('config.yaml'):
        logger.error("配置文件不存在，请先生成配置文件")
        logger.info("选项:")
        logger.info("1. 本地使用: 复制并编辑 config.yaml")
        logger.info("2. GitHub Actions: 设置相应的Secrets")
        return
    
    try:
        from multi_account_renewer import main as renewal_main
        await renewal_main()
    except Exception as e:
        logger.error(f"运行续期程序失败: {str(e)}")

def show_setup_guide():
    """显示设置指南"""
    guide_file = Path("SETUP_GUIDE.md")
    if guide_file.exists():
        print("\n" + "=" * 60)
        print("📖 设置指南")
        print("=" * 60)
        with open(guide_file, 'r', encoding='utf-8') as f:
            content = f.read()
            # 只显示前50行，避免输出过长
            lines = content.split('\n')[:50]
            print('\n'.join(lines))
            if len(content.split('\n')) > 50:
                print("\n... (更多内容请查看 SETUP_GUIDE.md 文件)")
    else:
        logger.error("设置指南文件不存在")

async def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        logger.error("请先安装依赖后重试")
        return 1
    
    # 设置目录
    setup_directories()
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-6): ").strip()

            if choice == '1':
                await run_session_generator()
            elif choice == '2':
                await run_bot_token_test()
            elif choice == '3':
                await run_config_test()
            elif choice == '4':
                await run_renewal()
            elif choice == '5':
                show_setup_guide()
            elif choice == '6':
                logger.info("再见!")
                break
            else:
                logger.warning("无效选择，请输入 1-6")
                
        except KeyboardInterrupt:
            logger.info("\n用户中断，退出程序")
            break
        except Exception as e:
            logger.error(f"发生错误: {str(e)}")
    
    return 0

if __name__ == "__main__":
    try:
        exit(asyncio.run(main()))
    except KeyboardInterrupt:
        print("\n程序被中断")
        exit(1)
