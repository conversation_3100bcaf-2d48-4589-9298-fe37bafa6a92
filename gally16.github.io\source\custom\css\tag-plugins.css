@media (min-width: 1200px) {
    .poem {
      margin: 0 auto;
      height: auto;
      writing-mode: vertical-rl;
      writing-mode: tb-rl;
    }
    .poem p {
      text-decoration: underline;
      text-decoration-color: rgba(193,11,11,0.72);
      text-decoration-style: dashed;
    }
  }
  @font-face {
    font-family: 'Poem';
    src: url("/custom/css/楷体_GB2312.ttf");
    font-display: swap;
  }
  .poem p {
    font-family: 'Poem', 'KaiTi', sans-serif !important;
    font-size: 25px;
    text-align: center;
  }
  .poem-title {
    font-family: 'Poem', 'KaiTi', sans-serif !important;
    font-size: 2.5em;
    text-align: center;
  }
  .poem-author {
    text-align: center !important;
    font-family: 'Poem', 'KaiTi', sans-serif !important;
    font-size: 16px;
    color: #424242;
  }