<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="generator" content="VuePress 2.0.0-beta.61">
    <style>
      :root {
        --c-bg: #fff;
      }
      html.dark {
        --c-bg: #22272e;
      }
      html, body {
        background-color: var(--c-bg);
      }
    </style>
    <script>
      const userMode = localStorage.getItem('vuepress-color-scheme');
			const systemDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
			if (userMode === 'dark' || (userMode !== 'light' && systemDarkMode)) {
				document.documentElement.classList.toggle('dark', true);
			}
    </script>
    <link rel="icon" href="/docs/images/c192.png"><title>站点基础配置(一) | 安知鱼主题指南</title><meta name="description" content="安知鱼主题站点基础配置">
    <link rel="preload" href="/anzhiyu-docs/assets/style-47b50212.css" as="style"><link rel="stylesheet" href="/anzhiyu-docs/assets/style-47b50212.css">
    <link rel="modulepreload" href="/anzhiyu-docs/assets/app-18744df2.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/framework-2fd1fcd7.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/site-configuration1.html-50f88905.js"><link rel="modulepreload" href="/anzhiyu-docs/assets/site-configuration1.html-f7afdcb5.js"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-e7463a10.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/page-configuration.html-cc2f4bd1.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/quick-start.html-ad03cacb.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-0505f6c4.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-d3638ef1.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-7a1b5d87.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration4.html-e8a4bf32.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-f9875e7b.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/index.html-f69e6992.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/page-configuration.html-89a1d5ab.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/quick-start.html-5dcde832.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/reward-list.html-2a772ecf.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration2.html-e0d0931a.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration3.html-74290865.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/site-configuration4.html-919542bb.js" as="script"><link rel="prefetch" href="/anzhiyu-docs/assets/404.html-bbf580d2.js" as="script">
  </head>
  <body>
    <div id="app"><!--[--><div class="theme-container"><!--[--><header class="navbar"><div class="toggle-sidebar-button" title="toggle sidebar" aria-expanded="false" role="button" tabindex="0"><div class="icon" aria-hidden="true"><span></span><span></span><span></span></div></div><span><a href="/anzhiyu-docs/" class=""><img class="logo" src="/anzhiyu-docs/./images/c192.png" alt="安知鱼主题指南"><span class="site-name can-hide">安知鱼主题指南</span></a></span><div class="navbar-items-wrapper" style=""><!--[--><!--]--><nav class="navbar-items can-hide"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><button class="toggle-color-mode-button" title="toggle color mode"><svg style="" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6z" fill="currentColor"></path><path d="M5.394 6.813l1.414-1.415l3.506 3.506L8.9 10.318z" fill="currentColor"></path><path d="M2 15.005h5v2H2z" fill="currentColor"></path><path d="M5.394 25.197L8.9 21.691l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 25.005h2v5h-2z" fill="currentColor"></path><path d="M21.687 23.106l1.414-1.415l3.506 3.506l-1.414 1.414z" fill="currentColor"></path><path d="M25 15.005h5v2h-5z" fill="currentColor"></path><path d="M21.687 8.904l3.506-3.506l1.414 1.415l-3.506 3.505z" fill="currentColor"></path><path d="M15 2.005h2v5h-2z" fill="currentColor"></path></svg><svg style="display:none;" class="icon" focusable="false" viewBox="0 0 32 32"><path d="M13.502 5.414a15.075 15.075 0 0 0 11.594 18.194a11.113 11.113 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1.002 1.002 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.072 13.072 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3z" fill="currentColor"></path></svg></button><form class="search-box" role="search"><input type="search" placeholder="Search" autocomplete="off" spellcheck="false" value><!----></form></div></header><!--]--><div class="sidebar-mask"></div><!--[--><aside class="sidebar"><nav class="navbar-items"><!--[--><div class="navbar-item"><a href="/anzhiyu-docs/" class="" aria-label="指南"><!--[--><!--]--> 指南 <!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://github.com/anzhiyu-c/anzhiyu-docs/" rel="noopener noreferrer" target="_blank" aria-label="Github"><!--[--><!--]--> Github <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><div class="navbar-item"><a class="external-link" href="https://blog.anheyu.com/" rel="noopener noreferrer" target="_blank" aria-label="作者博客"><!--[--><!--]--> 作者博客 <span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span><!--[--><!--]--></a></div><!--]--></nav><!--[--><!--]--><ul class="sidebar-items"><!--[--><li><a href="/anzhiyu-docs/" class="sidebar-item sidebar-heading" aria-label="简介"><!--[--><!--]--> 简介 <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/quick-start.html" class="sidebar-item sidebar-heading" aria-label="快速上手"><!--[--><!--]--> 快速上手 <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/page-configuration.html" class="sidebar-item sidebar-heading" aria-label="页面配置"><!--[--><!--]--> 页面配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html" class="router-link-active router-link-exact-active router-link-active sidebar-item sidebar-heading active" aria-label="站点配置(一)"><!--[--><!--]--> 站点配置(一) <!--[--><!--]--></a><ul style="" class="sidebar-item-children"><!--[--><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#语言" class="router-link-active router-link-exact-active sidebar-item" aria-label="语言"><!--[--><!--]--> 语言 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#网站资料" class="router-link-active router-link-exact-active sidebar-item" aria-label="网站资料"><!--[--><!--]--> 网站资料 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#导航配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="导航配置"><!--[--><!--]--> 导航配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#导航栏设置" class="router-link-active router-link-exact-active sidebar-item" aria-label="导航栏设置"><!--[--><!--]--> 导航栏设置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#代码块配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="代码块配置"><!--[--><!--]--> 代码块配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#图标配置" class="router-link-active router-link-exact-active sidebar-item" aria-label="图标配置"><!--[--><!--]--> 图标配置 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#顶部图" class="router-link-active router-link-exact-active sidebar-item" aria-label="顶部图"><!--[--><!--]--> 顶部图 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章置顶" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章置顶"><!--[--><!--]--> 文章置顶 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章封面" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章封面"><!--[--><!--]--> 文章封面 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章-meta-显示" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章 meta 显示"><!--[--><!--]--> 文章 meta 显示 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章版权" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章版权"><!--[--><!--]--> 文章版权 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章打赏" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章打赏"><!--[--><!--]--> 文章打赏 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#toc" class="router-link-active router-link-exact-active sidebar-item" aria-label="TOC"><!--[--><!--]--> TOC <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#相关文章" class="router-link-active router-link-exact-active sidebar-item" aria-label="相关文章"><!--[--><!--]--> 相关文章 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章过期提醒" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章过期提醒"><!--[--><!--]--> 文章过期提醒 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章编辑按钮" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章编辑按钮"><!--[--><!--]--> 文章编辑按钮 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#文章分页按钮" class="router-link-active router-link-exact-active sidebar-item" aria-label="文章分页按钮"><!--[--><!--]--> 文章分页按钮 <!--[--><!--]--></a><!----></li><li><a aria-current="page" href="/anzhiyu-docs/site-configuration1.html#中控台" class="router-link-active router-link-exact-active sidebar-item" aria-label="中控台"><!--[--><!--]--> 中控台 <!--[--><!--]--></a><!----></li><!--]--></ul></li><li><a href="/anzhiyu-docs/site-configuration2.html" class="sidebar-item sidebar-heading" aria-label="站点配置(二)"><!--[--><!--]--> 站点配置(二) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration3.html" class="sidebar-item sidebar-heading" aria-label="站点配置(三)"><!--[--><!--]--> 站点配置(三) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/site-configuration4.html" class="sidebar-item sidebar-heading" aria-label="站点配置(四)"><!--[--><!--]--> 站点配置(四) <!--[--><!--]--></a><!----></li><li><a href="/anzhiyu-docs/reward-list.html" class="sidebar-item sidebar-heading" aria-label="赞赏名单"><!--[--><!--]--> 赞赏名单 <!--[--><!--]--></a><!----></li><!--]--></ul><!--[--><!--]--></aside><!--]--><!--[--><main class="page"><!--[--><!--]--><div class="theme-default-content"><!--[--><!--]--><div><div class="hint-container warning"><p class="hint-container-title">警告</p><p>本教程更新于 2023 年 7 月 5 日，教程的内容针对最新的 anzhiyu 主题(如果你是旧版本，教程会有出入，请留意) 🐟 安知鱼 已经更新到 <a href="https://github.com/anzhiyu-c/hexo-theme-anzhiyu/releases/tag/1.4.0" target="_blank" rel="noopener noreferrer">1.4.0<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p></div><h2 id="语言" tabindex="-1"><a class="header-anchor" href="#语言" aria-hidden="true">#</a> 语言</h2><p>修改站点配置文件 <code>_config.yml</code>，不是主题配置文件。</p><p>默认语言是 en</p><p>主题支持三种语言</p><ul><li>default(en)</li><li>zh-CN (简体中文)</li><li>zh-TW (繁体中文)</li></ul><h2 id="网站资料" tabindex="-1"><a class="header-anchor" href="#网站资料" aria-hidden="true">#</a> 网站资料</h2><p>修改网站各种资料，例如标题、副标题和邮箱等个人资料，请修改博客根目录的<code>_config.yml</code>，请注意如果需要适配 pjax 必须填写<code>description</code>字段，以保证需要重载的 dom 数量一致。</p><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/05/642cfb7d42a1f.webp" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><h2 id="导航配置" tabindex="-1"><a class="header-anchor" href="#导航配置" aria-hidden="true">#</a> 导航配置</h2><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token key atrule">文章</span><span class="token punctuation">:</span>
    <span class="token key atrule">隧道</span><span class="token punctuation">:</span> /archives/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>box<span class="token punctuation">-</span>archive
    <span class="token key atrule">分类</span><span class="token punctuation">:</span> /categories/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>shapes
    <span class="token key atrule">标签</span><span class="token punctuation">:</span> /tags/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>tags

  <span class="token key atrule">友链</span><span class="token punctuation">:</span>
    <span class="token key atrule">友人帐</span><span class="token punctuation">:</span> /link/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>link
    <span class="token key atrule">朋友圈</span><span class="token punctuation">:</span> /fcircle/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>artstation
    <span class="token key atrule">留言板</span><span class="token punctuation">:</span> /comments/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>envelope

  <span class="token key atrule">我的</span><span class="token punctuation">:</span>
    <span class="token key atrule">音乐馆</span><span class="token punctuation">:</span> /music/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>music
    <span class="token key atrule">追番页</span><span class="token punctuation">:</span> /bangumis/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>bilibili
    <span class="token key atrule">相册集</span><span class="token punctuation">:</span> /album/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>images
    <span class="token key atrule">小空调</span><span class="token punctuation">:</span> /air<span class="token punctuation">-</span>conditioner/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>fan

  <span class="token key atrule">关于</span><span class="token punctuation">:</span>
    <span class="token key atrule">关于本人</span><span class="token punctuation">:</span> /about/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>paper<span class="token punctuation">-</span>plane
    <span class="token key atrule">闲言碎语</span><span class="token punctuation">:</span> /essay/ <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>lightbulb
    <span class="token key atrule">随便逛逛</span><span class="token punctuation">:</span> javascript<span class="token punctuation">:</span>toRandomPost() <span class="token punctuation">|</span><span class="token punctuation">|</span> anzhiyu<span class="token punctuation">-</span>icon<span class="token punctuation">-</span>shoe<span class="token punctuation">-</span>prints1
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>必须是 <code>/xxx/</code>，后面<code>||</code>分开，然后写图标名。</p><p>如果不希望显示图标，图标名可不写。</p><div class="hint-container warning"><p class="hint-container-title">警告</p><p>注意： 导航的文字可自行更改</p></div><p>例如：</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">menu</span><span class="token punctuation">:</span>
  <span class="token key atrule">Article</span><span class="token punctuation">:</span>
    <span class="token key atrule">Tunnel</span><span class="token punctuation">:</span> /archives/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>box<span class="token punctuation">-</span>archive
    <span class="token key atrule">Classification</span><span class="token punctuation">:</span> /categories/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>shapes
    <span class="token key atrule">Label</span><span class="token punctuation">:</span> /tags/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>tags

  <span class="token key atrule">Friend</span><span class="token punctuation">:</span>
    <span class="token key atrule">Friends account</span><span class="token punctuation">:</span> /link/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>link
    <span class="token key atrule">Moments</span><span class="token punctuation">:</span> /fcircle/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>artstation
    <span class="token key atrule">Message board</span><span class="token punctuation">:</span> /comments/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>envelope

  <span class="token key atrule">My</span><span class="token punctuation">:</span>
    <span class="token key atrule">Music Hall</span><span class="token punctuation">:</span> /music/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>music
    <span class="token key atrule">Chasing</span><span class="token punctuation">:</span> /bangumis/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>bilibili1
    <span class="token key atrule">Album Set</span><span class="token punctuation">:</span> /album/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>images
    <span class="token key atrule">Conditioning</span><span class="token punctuation">:</span> /air<span class="token punctuation">-</span>conditioner/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>fan

  <span class="token key atrule">About</span><span class="token punctuation">:</span> /about/ <span class="token punctuation">|</span><span class="token punctuation">|</span> icon<span class="token punctuation">-</span>zhifeiji
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433a6d2041e3.png" alt="导航菜单相关配置" tabindex="0" loading="lazy"><figcaption>导航菜单相关配置</figcaption></figure><h2 id="导航栏设置" tabindex="-1"><a class="header-anchor" href="#导航栏设置" aria-hidden="true">#</a> 导航栏设置</h2><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># nav相关配置</span>
<span class="token key atrule">nav</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">travelling</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">clock</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">menu</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">title</span><span class="token punctuation">:</span> 网页
      <span class="token key atrule">item</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 博客
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//blog.anheyu.com/
          <span class="token key atrule">icon</span><span class="token punctuation">:</span> /img/favicon.png
    <span class="token punctuation">-</span> <span class="token key atrule">title</span><span class="token punctuation">:</span> 项目
      <span class="token key atrule">item</span><span class="token punctuation">:</span>
        <span class="token punctuation">-</span> <span class="token key atrule">name</span><span class="token punctuation">:</span> 安知鱼图床
          <span class="token key atrule">link</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//image.anheyu.com/
          <span class="token key atrule">icon</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//image.anheyu.com/favicon.ico
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>解释</th></tr></thead><tbody><tr><td>enable</td><td>是否启用 nav 左侧项目按钮，仅控制左侧项目按钮</td></tr><tr><td>travelling</td><td>是否启用 nav 开往按钮</td></tr><tr><td>clock</td><td>是否启用 nav 左侧<a href="https://widget.qweather.com/" target="_blank" rel="noopener noreferrer">和风天气<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></td></tr><tr><td>menu</td><td>nav 左侧项目按钮内的菜单</td></tr><tr><td>menu.title</td><td>nav 左侧项目按钮内的菜单标题</td></tr><tr><td>menu.item</td><td>nav 左侧项目按钮内的菜单项</td></tr><tr><td>menu.item.name</td><td>nav 左侧项目按钮内的菜单项标题</td></tr><tr><td>menu.item.link</td><td>nav 左侧项目按钮内的菜单项链接</td></tr><tr><td>menu.item.icon</td><td>nav 左侧项目按钮内的菜单项图标</td></tr></tbody></table><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433aa002f288.webp" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><h2 id="代码块配置" tabindex="-1"><a class="header-anchor" href="#代码块配置" aria-hidden="true">#</a> 代码块配置</h2><div class="hint-container info"><p class="hint-container-title">相关信息</p><p>代码块中的所有功能只适用于 Hexo 自带的代码渲染 如果使用第三方的渲染器，不一定会有效</p></div><h3 id="代码高亮主题" tabindex="-1"><a class="header-anchor" href="#代码高亮主题" aria-hidden="true">#</a> 代码高亮主题</h3><p><code>AnZhiYu</code> 支持 6 种代码高亮样式：</p><ul><li>darker</li><li>pale night</li><li>light</li><li>ocean</li><li>mac</li><li>mac light</li></ul><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">highlight_theme</span><span class="token punctuation">:</span> light
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-211-0" aria-selected="true">darker</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-211-1" aria-selected="false">pale night</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-211-2" aria-selected="false">light</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-211-3" aria-selected="false">ocean</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-211-4" aria-selected="false">mac</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-211-5" aria-selected="false">mac light</button></div><!--[--><div class="tab-item active" id="tab-211-0" role="tabpanel" aria-expanded="true"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433acf03c029.png" alt="darker" tabindex="0" loading="lazy"><figcaption>darker</figcaption></figure></div><div class="tab-item" id="tab-211-1" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433acf02c312.png" alt="pale night" tabindex="0" loading="lazy"><figcaption>pale night</figcaption></figure></div><div class="tab-item" id="tab-211-2" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433acf0382d1.png" alt="light" tabindex="0" loading="lazy"><figcaption>light</figcaption></figure></div><div class="tab-item" id="tab-211-3" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433acf03185f.png" alt="ocean" tabindex="0" loading="lazy"><figcaption>ocean</figcaption></figure></div><div class="tab-item" id="tab-211-4" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433acf031c0d.png" alt="mac" tabindex="0" loading="lazy"><figcaption>mac</figcaption></figure></div><div class="tab-item" id="tab-211-5" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433acf038752.png" alt="mac light" tabindex="0" loading="lazy"><figcaption>mac light</figcaption></figure></div><!--]--></div><h3 id="代码复制" tabindex="-1"><a class="header-anchor" href="#代码复制" aria-hidden="true">#</a> 代码复制</h3><p>主题支持代码复制功能，修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">highlight_copy</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433ae3ceca9b.png" alt="代码复制" tabindex="0" loading="lazy"><figcaption>代码复制</figcaption></figure><h3 id="代码框展开-关闭" tabindex="-1"><a class="header-anchor" href="#代码框展开-关闭" aria-hidden="true">#</a> 代码框展开/关闭</h3><p>在默认情况下，代码框自动展开，可设置是否所有代码框都关闭状态，点击&gt;可展开代码</p><ul><li>true 全部代码框不展开，需点击&gt;打开</li><li>false 代码框展开，有&gt;点击按钮</li><li>none 不显示&gt;按钮</li></ul><p>修改 主题配置文件</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">highlight_shrink</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment">#代码框不展开，需点击 &#39;&gt;&#39; 打开</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><div class="hint-container info"><p class="hint-container-title">相关信息</p><p>你也可以在 post/page 页对应的 markdown 文件 front-matter 添加 highlight_shrink 来独立配置。</p><p>当<strong>主题配置文件</strong>中的 <code>highlight_shrink</code> 设为 true 时，可在 front-matter 添加 <code>highlight_shrink: false</code> 来单独配置文章展开代码框。</p><p>当<strong>主题配置文件</strong>中的 <code>highlight_shrin</code>k 设为 false 时，可在 front-matter 添加 <code>highlight_shrink: true </code>来单独配置文章收缩代码框。</p></div><p><code>highlight_shrink: true</code><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433b421da871.png!blogimg" alt="" loading="lazy"><code>highlight_shrink: false</code><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433b421dea9b.png!blogimg" alt="" loading="lazy"><code>highlight_shrink: none</code><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433b421df10e.png!blogimg" alt="" loading="lazy"></p><h3 id="代码换行" tabindex="-1"><a class="header-anchor" href="#代码换行" aria-hidden="true">#</a> 代码换行</h3><p>在默认情况下，Hexo 在编译的时候不会实现代码自动换行。如果你不希望在代码块的区域里有横向滚动条的话，那么你可以考虑开启这个功能。</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">code_word_wrap</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>如果你是使用 <code>highlight</code> 渲染，需要找到你站点的 Hexo 配置文件<code>_config.yml</code>，将 <code>line_number</code> 改成 <code>false</code>:</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">highlight</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">line_number</span><span class="token punctuation">:</span> <span class="token boolean important">false</span> <span class="token comment"># &lt;- 改这里</span>
  <span class="token key atrule">auto_detect</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">tab_replace</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>如果你是使用 <code>prismjs</code> 渲染，需要找到你站点的 Hexo 配置文件<code>_config.yml</code>，将 <code>line_number</code> 改成 <code>false</code>:</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">prismjs</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">preprocess</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">line_number</span><span class="token punctuation">:</span> <span class="token boolean important">false</span> <span class="token comment"># &lt;- 改这里</span>
  <span class="token key atrule">tab_replace</span><span class="token punctuation">:</span> <span class="token string">&quot;&quot;</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><blockquote><p>设置<code>code_word_wrap</code>之前:</p></blockquote><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433b76aa968f.png!blogimg" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><blockquote><p>设置<code>code_word_wrap</code>之后:</p></blockquote><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433b76aa955d.png!blogimg" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><h3 id="代码高度限制" tabindex="-1"><a class="header-anchor" href="#代码高度限制" aria-hidden="true">#</a> 代码高度限制</h3><p>可配置代码高度限制，超出的部分会隐藏，并显示展开按钮，默认 330，可配置为 <code>false</code>。</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">highlight_height_limit</span><span class="token punctuation">:</span> <span class="token boolean important">false</span> <span class="token comment"># unit: px</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>注意：</p><ol><li><p>单位是 <code>px</code>，直接添加数字，如 200</p></li><li><p>实际限制高度为 <code>highlight_height_limit + 30 px</code> ，多增加 30px 限制，目的是避免代码高度只超出 highlight_height_limit 一点时，出现展开按钮，展开没内容。</p></li><li><p>不适用于隐藏后的代码块（ css 设置 display: none）</p></li></ol><h2 id="图标配置" tabindex="-1"><a class="header-anchor" href="#图标配置" aria-hidden="true">#</a> 图标配置</h2><p>AnZhiYu 支持 <a href="https://www.iconfont.cn/collections/detail?cid=44481" target="_blank" rel="noopener noreferrer">阿里图标<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> (需配置自己的图标)，与 <a href="https://fontawesome.com/icons?from=io" target="_blank" rel="noopener noreferrer">font-awesome v6<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a> 图标(需开启<code>fontawesome</code>)，使用阿里图标需配置主题配置文件中<code>icon.ali_iconfont_js</code>字段，默认内置部分图标，修改主题配置文件，视频教程: <a href="https://www.bilibili.com/video/BV1Cv4y1n7FW/?spm_id_from=333.999.0.0&amp;vd_source=4d9717102296e4b7a60ecdfad55ae2dd" target="_blank" rel="noopener noreferrer">安知鱼主题社交图标配置<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">icons</span><span class="token punctuation">:</span>
  <span class="token key atrule">ali_iconfont_js</span><span class="token punctuation">:</span> <span class="token comment"># 阿里图标symbol 引用链接，主题会进行加载 symbol 引用</span>
  <span class="token key atrule">fontawesome</span><span class="token punctuation">:</span> <span class="token boolean important">false</span> <span class="token comment">#是否启用fontawesome6图标</span>
  <span class="token key atrule">fontawesome_animation_css</span><span class="token punctuation">:</span> <span class="token comment">#fontawesome_animation 如果有就会加载，示例值：https://npm.elemecdn.com/hexo-butterfly-tag-plugins-plus@1.0.17/lib/assets/font-awesome-animation.min.css</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>内置阿里图标库：<a href="https://www.iconfont.cn/collections/detail?cid=44481" target="_blank" rel="noopener noreferrer">https://www.iconfont.cn/collections/detail?cid=44481<span><svg class="external-link-icon" xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path><polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg><span class="external-link-icon-sr-only">open in new window</span></span></a></p><p>使用方法，将图标库中的图标名复制，然后加上前缀<code>anzhiyu-</code>即可，比如<code>icon-github</code>图标，则为<code>anzhiyu-icon-github</code>。</p><p>社交图标，书写格式 <code>名称：url || 描述性文字 || icon名称</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># social settings (社交图标设置)</span>
<span class="token comment"># formal:</span>
<span class="token comment">#   name: link || icon</span>
<span class="token key atrule">social</span><span class="token punctuation">:</span>
  <span class="token comment"># Github: https://github.com/anzhiyu-c || anzhiyu-icon-github</span>
  <span class="token comment"># Email: https://mail.qq.com/cgi-bin/qm_share?t=qm_mailme&amp;email=<EMAIL> || anzhiyu-icon-envelope</span>
  <span class="token comment"># RSS: atom.xml || anzhiyu-icon-rss</span>
  <span class="token comment"># BiliBili: https://space.bilibili.com/372204786 || anzhiyu-icon-bilibili</span>
  <span class="token comment"># QQ: tencent://Message/?Uin=2268025923&amp;amp;websiteName=local.edu.com:8888=&amp;amp;Menu=yes || anzhiyu-icon-qq</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>如需 hover 动画生效需配置<code>fontawesome_animation_css</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">icons</span><span class="token punctuation">:</span>
  <span class="token key atrule">ali_iconfont_js</span><span class="token punctuation">:</span> <span class="token comment"># 阿里图标symbol 引用链接，主题会进行加载 symbol 引用</span>
  <span class="token key atrule">fontawesome</span><span class="token punctuation">:</span> <span class="token boolean important">false</span> <span class="token comment">#是否启用fontawesome6图标</span>
  <span class="token key atrule">fontawesome_animation_css</span><span class="token punctuation">:</span> <span class="token comment">#fontawesome_animation 如果有就会加载，示例值：https://npm.elemecdn.com/hexo-butterfly-tag-plugins-plus@1.0.17/lib/assets/font-awesome-animation.min.css</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h2 id="顶部图" tabindex="-1"><a class="header-anchor" href="#顶部图" aria-hidden="true">#</a> 顶部图</h2><div class="hint-container info"><p class="hint-container-title">相关信息</p><p>如果不要显示顶部图，可直接配置 <code>disable_top_img: true</code></p></div><div class="hint-container tip"><p class="hint-container-title">提示</p><p>顶部图的获取顺序，如果都没有配置，则不显示顶部图。</p><ol><li><p>页面顶部图的获取顺序：</p><p><code>各自配置的 top_img &gt; 配置文件的 default_top_img</code></p></li><li><p>文章页顶部图的获取顺序：</p><p><code>各自配置的 top_img &gt; cover &gt; 配置文件的 default_top_img</code></p></li></ol></div><p>配置中的值：</p><table><thead><tr><th>配置</th><th>解释</th></tr></thead><tbody><tr><td>index_img</td><td>主页的 top_img，示例值: index_img: &quot;background: url(https://img02.anheyu.com/xxx) top / cover no-repeat&quot;</td></tr><tr><td>default_top_img</td><td>默认的 top_img，当页面的 top_img 没有配置时，会显示 default_top_img</td></tr><tr><td>archive_img</td><td>归档页面的 top_img</td></tr><tr><td>tag_img</td><td>tag 子页面 的 默认 top_img</td></tr><tr><td>tag_per_img</td><td>tag 子页面的 top_img，可配置每个 tag 的 top_img</td></tr><tr><td>category_img</td><td>category 子页面 的 默认 top_img</td></tr><tr><td>category_per_img</td><td>category 子页面的 top_img，可配置每个 category 的 top_img</td></tr></tbody></table><p>其它页面 （tags/categories/自建页面）和 文章页 的 <code>top_img</code> ，请到对应的 md 页面设置 <code>front-matter</code> 中的 <code>top_img</code></p><p>以上所有的 <code>top_img</code> 可配置以下值</p><table><thead><tr><th>配置的值</th><th>效果</th></tr></thead><tbody><tr><td>留空</td><td>显示默认的 top_img（如有），否则显示默认的顔色<br>（文章页 top_img 留空的话，会显示 cover 的值）</td></tr><tr><td>img 链接</td><td>图片的链接，显示所配置的图片</td></tr><tr><td>顔色(<br>HEX 值 - #0000FF<br>RGB 值 - rgb(0,0,255)<br>顔色单词 - orange<br>渐变色 - linear-gradient( 135deg, #E2B0FF 10%, #9F44D3 100%)</td><td>对应的顔色</td></tr><tr><td>transparent</td><td>透明</td></tr><tr><td>false</td><td>不显示 top_img</td></tr></tbody></table><p>并不推荐为每个 tag 和每个 category 都配置不同的顶部图，因为配置太多会拖慢生成速度</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code>tag_per_img：
  <span class="token key atrule">aplayer</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//xxxxxx.png
  <span class="token key atrule">android</span><span class="token punctuation">:</span> ddddddd.png

category_per_img：
  <span class="token key atrule">随想</span><span class="token punctuation">:</span> hdhdh.png
  <span class="token key atrule">推荐</span><span class="token punctuation">:</span> ddjdjdjd.png
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h2 id="文章置顶" tabindex="-1"><a class="header-anchor" href="#文章置顶" aria-hidden="true">#</a> 文章置顶</h2><p>【推荐】<code>hexo-generator-index</code> 从 2.0.0 开始，已经支持文章置顶功能。你可以直接在文章的 <code>front-matter</code> 区域里添加 <code>sticky: 1</code> 属性来把这篇文章置顶。数值越大，置顶的优先级越大。</p><h2 id="文章封面" tabindex="-1"><a class="header-anchor" href="#文章封面" aria-hidden="true">#</a> 文章封面</h2><p>文章的 markdown 文档上,在 <code>Front-matter</code> 添加 <code>cover</code> ,并填上要显示的图片地址。</p><p>如果不配置 <code>cover</code>,可以设置显示默认的 cover。</p><p>如果不想在首页显示 cover, 可以设置为 <code>false。</code></p><blockquote><p>文章封面的获取顺序 <code>Front-matter</code> 的 <code>cover</code> &gt; <code>配置文件的 default_cover</code> &gt; <code>false</code></p></blockquote><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">cover</span><span class="token punctuation">:</span>
  <span class="token comment"># 是否显示文章封面</span>
  <span class="token key atrule">index_enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">aside_enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">archives_enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token comment"># 封面显示的位置</span>
  <span class="token comment"># 三个值可配置 left , right , both</span>
  <span class="token key atrule">position</span><span class="token punctuation">:</span> both
  <span class="token comment"># 当没有设置cover时，默认的封面显示</span>
  <span class="token key atrule">default_cover</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>参数</th><th>解释</th></tr></thead><tbody><tr><td>index_enable</td><td>主页是否显示文章封面图</td></tr><tr><td>aside_enable</td><td>侧栏是否显示文章封面图</td></tr><tr><td>archives_enable</td><td>归档页面是否显示文章封面图</td></tr><tr><td>position</td><td>主页卡片文章封面的显示位置<br>- left：全部显示在左边<br>- right：全部显示在右边<br>- both：封面位置以左右左右轮流显示</td></tr><tr><td>default_cover</td><td>默认的 cover, 可配置图片链接/顔色/渐变色等</td></tr></tbody></table><p>当配置多张图片时,会随机选择一张作为 cover.此时写法应为</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">default_cover</span><span class="token punctuation">:</span>
  <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//file.crazywong.com/gh/jerryc127/CDN@latest/cover/default_bg.png
  <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//file.crazywong.com/gh/jerryc127/CDN@latest/cover/default_bg2.png
  <span class="token punctuation">-</span> https<span class="token punctuation">:</span>//file.crazywong.com/gh/jerryc127/CDN@latest/cover/default_bg3.png
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433ddeabbae4.webp!blogimg" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><h2 id="文章-meta-显示" tabindex="-1"><a class="header-anchor" href="#文章-meta-显示" aria-hidden="true">#</a> 文章 meta 显示</h2><p>这个选项是用来显示文章的相关信息的。</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">post_meta</span><span class="token punctuation">:</span>
  <span class="token key atrule">page</span><span class="token punctuation">:</span>
    <span class="token key atrule">date_type</span><span class="token punctuation">:</span> both <span class="token comment"># created or updated or both 主页文章日期是创建日或者更新日或都显示</span>
    <span class="token key atrule">date_format</span><span class="token punctuation">:</span> relative <span class="token comment"># date/relative 显示日期还是相对日期</span>
    <span class="token key atrule">categories</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># true or false 主页是否显示分类</span>
    <span class="token key atrule">tags</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># true or false 主页是否显示标签</span>
    <span class="token key atrule">label</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># true or false 显示描述性文字</span>
  <span class="token key atrule">post</span><span class="token punctuation">:</span>
    <span class="token key atrule">date_type</span><span class="token punctuation">:</span> both <span class="token comment"># created or updated or both 文章页日期是创建日或者更新日或都显示</span>
    <span class="token key atrule">date_format</span><span class="token punctuation">:</span> relative <span class="token comment"># date/relative 显示日期还是相对日期</span>
    <span class="token key atrule">categories</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># true or false 文章页是否显示分类</span>
    <span class="token key atrule">tags</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># true or false 文章页是否显示标签</span>
    <span class="token key atrule">label</span><span class="token punctuation">:</span> <span class="token boolean important">true</span> <span class="token comment"># true or false 显示描述性文字</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-642-0" aria-selected="true">主页</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-642-1" aria-selected="false">文章页</button></div><!--[--><div class="tab-item active" id="tab-642-0" role="tabpanel" aria-expanded="true"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433ede80d024.webp" alt="主页" tabindex="0" loading="lazy"><figcaption>主页</figcaption></figure></div><div class="tab-item" id="tab-642-1" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433ede8166d0.png" alt="文章页" tabindex="0" loading="lazy"><figcaption>文章页</figcaption></figure></div><!--]--></div><p><code>date_format</code>配置时间显示明确时间还是相对时间</p><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-657-0" aria-selected="true">相对时间</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-657-1" aria-selected="false">明确时间</button></div><!--[--><div class="tab-item active" id="tab-657-0" role="tabpanel" aria-expanded="true"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433eeb22fcca.webp!blogimg" alt="相对时间" tabindex="0" loading="lazy"><figcaption>相对时间</figcaption></figure></div><div class="tab-item" id="tab-657-1" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433eeb22fd0f.webp!blogimg" alt="明确时间" tabindex="0" loading="lazy"><figcaption>明确时间</figcaption></figure></div><!--]--></div><h2 id="文章版权" tabindex="-1"><a class="header-anchor" href="#文章版权" aria-hidden="true">#</a> 文章版权</h2><p>为你的博客文章展示文章版权和许可协议。</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">post_copyright</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">decode</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">author_href</span><span class="token punctuation">:</span>
  <span class="token key atrule">license</span><span class="token punctuation">:</span> CC BY<span class="token punctuation">-</span>NC<span class="token punctuation">-</span>SA 4.0
  <span class="token key atrule">license_url</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//creativecommons.org/licenses/by<span class="token punctuation">-</span>nc<span class="token punctuation">-</span>sa/4.0/
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p>由于 <code>Hexo 4.1</code> 开始，默认对网址进行解码，以至于如果是中文网址，会被解码，可设置 <code>decode: true</code> 来显示中文网址。</p><p>如果有文章（例如：转载文章）不需要显示版权，可以在文章 <code>Front-matter</code> 单独设置</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">copyright</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div></div></div><p>支持对单独文章设置版权信息，可以在文章 <code>Front-matter</code> 单独设置</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">copyright_author</span><span class="token punctuation">:</span> xxxx
<span class="token key atrule">copyright_author_href</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//xxxxxx.com
<span class="token key atrule">copyright_url</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//xxxxxx.com
<span class="token key atrule">copyright_info</span><span class="token punctuation">:</span> 此文章版权归xxxxx所有，如有转载，请注明来自原作者
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><strong>版权显示截图</strong></p><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433efcd40633.png!blogimg" alt="版权显示截图" tabindex="0" loading="lazy"><figcaption>版权显示截图</figcaption></figure><h2 id="文章打赏" tabindex="-1"><a class="header-anchor" href="#文章打赏" aria-hidden="true">#</a> 文章打赏</h2><p>在你每篇文章的结尾，可以添加打赏按钮。相关二维码可以自行配置。</p><p>对于没有提供二维码的，可配置一张软件的 icon 图片，然后在 link 上添加相应的打赏链接。用户点击图片就会跳转到链接去。</p><p>link 可以不写，会默认为图片的链接。coinAudio 为投币的音频。</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">reward</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">coinAudio</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/akilar<span class="token punctuation">-</span>candyassets@1.0.36/audio/aowu.m4a
  <span class="token key atrule">QR_code</span><span class="token punctuation">:</span>
    <span class="token punctuation">-</span> <span class="token key atrule">img</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog@1.1.6/img/post/common/qrcode<span class="token punctuation">-</span>weichat.png
      <span class="token key atrule">link</span><span class="token punctuation">:</span>
      <span class="token key atrule">text</span><span class="token punctuation">:</span> wechat
    <span class="token punctuation">-</span> <span class="token key atrule">img</span><span class="token punctuation">:</span> https<span class="token punctuation">:</span>//npm.elemecdn.com/anzhiyu<span class="token punctuation">-</span>blog@1.1.6/img/post/common/qrcode<span class="token punctuation">-</span>alipay.png
      <span class="token key atrule">link</span><span class="token punctuation">:</span>
      <span class="token key atrule">text</span><span class="token punctuation">:</span> alipay
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f0b4b8693.png" alt="文章打赏截图" tabindex="0" loading="lazy"><figcaption>文章打赏截图</figcaption></figure><h2 id="toc" tabindex="-1"><a class="header-anchor" href="#toc" aria-hidden="true">#</a> TOC</h2><p>在文章页，会有一个目录，用于显示 TOC。修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">toc</span><span class="token punctuation">:</span>
  <span class="token key atrule">post</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">page</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">number</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">expand</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token key atrule">style_simple</span><span class="token punctuation">:</span> <span class="token boolean important">false</span> <span class="token comment"># for post</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th>属性</th><th>解释</th></tr></thead><tbody><tr><td>post</td><td>文章页是否显示 TOC</td></tr><tr><td>page</td><td>普通页面是否显示 TOC</td></tr><tr><td>number</td><td>是否显示章节数</td></tr><tr><td>expand</td><td>是否展开 TOC</td></tr><tr><td>style_simple</td><td>简洁模式（侧边栏只显示 TOC, 只对文章页有效 ）</td></tr></tbody></table><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-776-0" aria-selected="true">Toc PC</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-776-1" aria-selected="false">Toc Mobile</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-776-2" aria-selected="false">style_simple: true</button></div><!--[--><div class="tab-item active" id="tab-776-0" role="tabpanel" aria-expanded="true"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f2bea649b.png!blogimg" alt="Toc PC" tabindex="0" loading="lazy"><figcaption>Toc PC</figcaption></figure></div><div class="tab-item" id="tab-776-1" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f2be9811f.png!blogimg" alt="Toc Mobile" tabindex="0" loading="lazy"><figcaption>Toc Mobile</figcaption></figure></div><div class="tab-item" id="tab-776-2" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f2beaa447.png!blogimg" alt="style_simple: true" tabindex="0" loading="lazy"><figcaption>style_simple: true</figcaption></figure></div><!--]--></div><p><strong>为特定的文章配置</strong></p><p>在你的文章 md 文件的头部，加入 toc_number 和 toc，并配置 true 或者 false 即可。</p><p>主题会优先判断文章 Markdown 的 Front-matter 是否有配置，如有，则以 Front-matter 的配置为准。否则，以主题配置文件中的配置为准</p><h2 id="相关文章" tabindex="-1"><a class="header-anchor" href="#相关文章" aria-hidden="true">#</a> 相关文章</h2><div class="hint-container warning"><p class="hint-container-title">警告</p><p>当文章封面设置为 <code>false</code> 时，或者没有获取到封面配置，相关文章背景将会显示主题色。</p></div><p>相关文章推荐的原理是根据文章 tags 的比重来推荐</p><p>修改 <code>主题配置文件</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token key atrule">related_post</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">limit</span><span class="token punctuation">:</span> <span class="token number">6</span> <span class="token comment"># 显示推荐文章数目</span>
  <span class="token key atrule">date_type</span><span class="token punctuation">:</span> created <span class="token comment"># or created or updated 文章日期显示创建日或者更新日</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f4e1be3af.webp!blogimg" alt="相关文章截图" tabindex="0" loading="lazy"><figcaption>相关文章截图</figcaption></figure><h2 id="文章过期提醒" tabindex="-1"><a class="header-anchor" href="#文章过期提醒" aria-hidden="true">#</a> 文章过期提醒</h2><p>可设置是否显示文章过期提醒，以更新时间为基准。</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># Displays outdated notice for a post (文章过期提醒)</span>
<span class="token key atrule">noticeOutdate</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
  <span class="token key atrule">style</span><span class="token punctuation">:</span> flat <span class="token comment"># style: simple/flat</span>
  <span class="token key atrule">limit_day</span><span class="token punctuation">:</span> <span class="token number">365</span> <span class="token comment"># When will it be shown</span>
  <span class="token key atrule">position</span><span class="token punctuation">:</span> top <span class="token comment"># position: top/bottom</span>
  <span class="token key atrule">message_prev</span><span class="token punctuation">:</span> It has been
  <span class="token key atrule">message_next</span><span class="token punctuation">:</span> days since the last update<span class="token punctuation">,</span> the content of the article may be outdated.
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><p><code>limit_day</code>： 距离更新时间多少天才显示文章过期提醒</p><p><code>message_prev</code>： 天数之前的文字</p><p><code>message_next</code>：天数之后的文字</p><div class="tab-list"><div class="tab-list-nav" role="tablist"><button type="button" class="tab-list-nav-item active" role="tab" aria-controls="tab-836-0" aria-selected="true">style: flat</button><button type="button" class="tab-list-nav-item" role="tab" aria-controls="tab-836-1" aria-selected="false">style: simple</button></div><!--[--><div class="tab-item active" id="tab-836-0" role="tabpanel" aria-expanded="true"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f770b38e8.webp!blogimg" alt="style: flat" tabindex="0" loading="lazy"><figcaption>style: flat</figcaption></figure></div><div class="tab-item" id="tab-836-1" role="tabpanel" aria-expanded="false"><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f770b2017.webp!blogimg" alt="style: simple" tabindex="0" loading="lazy"><figcaption>style: simple</figcaption></figure></div><!--]--></div><h2 id="文章编辑按钮" tabindex="-1"><a class="header-anchor" href="#文章编辑按钮" aria-hidden="true">#</a> 文章编辑按钮</h2><p>在文章标题旁边显示一个编辑按钮，点击会跳转到对应的链接去。</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># Post edit</span>
<span class="token comment"># Easily browse and edit blog source code online.</span>
<span class="token key atrule">post_edit</span><span class="token punctuation">:</span>
  <span class="token key atrule">enable</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
  <span class="token comment"># url: https://github.com/user-name/repo-name/edit/branch-name/subdirectory-name/</span>
  <span class="token comment"># For example: https://github.com/jerryc127/butterfly.js.org/edit/main/source/</span>
  <span class="token key atrule">url</span><span class="token punctuation">:</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f83e60756.webp" alt="文章编辑按钮截图" tabindex="0" loading="lazy"><figcaption>文章编辑按钮截图</figcaption></figure><h2 id="文章分页按钮" tabindex="-1"><a class="header-anchor" href="#文章分页按钮" aria-hidden="true">#</a> 文章分页按钮</h2><div class="hint-container warning"><p class="hint-container-title">警告</p><p>当文章封面设置为 <code>false</code> 时，或者没有获取到封面配置，分页背景将会显示主题色。</p></div><p>可设置分页的逻辑，也可以关闭分页显示</p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># post_pagination (分页)</span>
<span class="token comment"># value: 1 || 2 || false</span>
<span class="token comment"># 1: The &#39;next post&#39; will link to old post</span>
<span class="token comment"># 2: The &#39;next post&#39; will link to new post</span>
<span class="token comment"># false: disable pagination</span>
<span class="token key atrule">post_pagination</span><span class="token punctuation">:</span> <span class="token boolean important">false</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><table><thead><tr><th style="text-align:center;">参数</th><th style="text-align:center;">解释</th></tr></thead><tbody><tr><td style="text-align:center;">post_pagination: false</td><td style="text-align:center;">关闭分页按钮</td></tr><tr><td style="text-align:center;">post_pagination: 1</td><td style="text-align:center;">下一篇显示的是旧文章</td></tr><tr><td style="text-align:center;">post_pagination: 2</td><td style="text-align:center;">下一篇显示的是新文章</td></tr></tbody></table><figure><img src="https://img02.anheyu.com/adminuploads/1/2023/04/10/6433f8f0876ae.webp" alt="文章分页截图" tabindex="0" loading="lazy"><figcaption>文章分页截图</figcaption></figure><h2 id="中控台" tabindex="-1"><a class="header-anchor" href="#中控台" aria-hidden="true">#</a> 中控台</h2><p>主题配置文件中，默认为<code>true</code></p><div class="language-yaml line-numbers-mode" data-ext="yml"><pre class="language-yaml"><code><span class="token comment"># 中控台</span>
<span class="token key atrule">centerConsole</span><span class="token punctuation">:</span> <span class="token boolean important">true</span>
</code></pre><div class="line-numbers" aria-hidden="true"><div class="line-number"></div><div class="line-number"></div></div></div><p>中控台在小屏幕状态下只会显示功能按键</p><figure><img src="https://bu.dusays.com/2023/07/05/64a4d19e0536f.webp" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure><p>当屏幕足够大的时候，就能够显示<code>兴趣点</code>、<code>最近评论</code>、<code>时间归档</code>、<code>功能按键</code>、<code>音乐</code>等内容</p><figure><img src="https://bu.dusays.com/2023/07/05/64a4d19e0536f.webp" alt="" tabindex="0" loading="lazy"><figcaption></figcaption></figure></div><!--[--><!--]--></div><footer class="page-meta"><!----><div class="meta-item last-updated"><span class="meta-item-label">上次更新: </span><!----></div><div class="meta-item contributors"><span class="meta-item-label">贡献者: </span><span class="meta-item-info"><!--[--><!--[--><span class="contributor" title="email: <EMAIL>">anzhiyu</span><!--[-->, <!--]--><!--]--><!--[--><span class="contributor" title="email: <EMAIL>">Kouseki</span><!----><!--]--><!--]--></span></div></footer><nav class="page-nav"><p class="inner"><span class="prev"><a href="/anzhiyu-docs/page-configuration.html" class="" aria-label="页面配置"><!--[--><!--]--> 页面配置 <!--[--><!--]--></a></span><span class="next"><a href="/anzhiyu-docs/site-configuration2.html" class="" aria-label="站点配置(二)"><!--[--><!--]--> 站点配置(二) <!--[--><!--]--></a></span></p></nav><!--[--><!--]--></main><!--]--></div><!----><!--]--></div>
    <script type="module" src="/anzhiyu-docs/assets/app-18744df2.js" defer></script>
  </body>
</html>
