# 部署名称随意
name: 自动发布-auto-deploay-hexo
# 触发条件：在 push 到 master 分支后触发
on:
  push:
    branches: 
      - master  # 可以添加多个分支
# 设置时区
env:
  TZ: Asia/Shanghai

# 任务
jobs:
  blog-deploy:
  
    name: Hexo blog build & deploy

    runs-on: ubuntu-latest # 使用最新的 Ubuntu 系统作为编译部署的环境
    
    steps:
      - name: Checkout codes
        uses: actions/checkout@v3
        with:
          submodules: true # 加载子模块，避免初始化过程中的额外加载。

      - name: Sync local file timestamps
        run: |
          git ls-files -z | while read -d '' path; do touch -d $(git log -1 --format="@%ct" "$path") "$path"; done

      - name: Setup node
        # 设置 node.js 环境
        uses: actions/setup-node@v1
        with:
          node-version: '16.x'

      # 下载网站源码
      - name: Cache node modules
        # 设置包缓存目录，避免每次下载
        uses: actions/cache@v1
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

      # 安装依赖
      - name: Install hexo dependencies
        # 下载 hexo-cli 脚手架及相关安装包
        # hexo-offline pwa 依赖
        # hexo-abbrlink 文章短链接
        # hexo-blog-encrypt 文章加密插件
        # rss/atom 订阅
        run: |
          npm install -g hexo-cli
          npm install --no-fund
          npm install hexo-renderer-pug hexo-renderer-stylus --save
          npm install hexo-generator-search --save
          npm install hexo-abbrlink --save
          npm install hexo-offline --save
          npm install --save hexo-blog-encrypt
          npm i cheerio --save
          npm install --save hexo-generator-feed
          npm ls --depth 0

          #gulp
          npm install --global gulp-cli #全局安装gulp指令集
          npm install gulp --save #安装gulp插件
          # 用gulp-html-minifier-terser可以压缩HTML中的ES6语法
          npm install gulp-htmlclean --save-dev
          npm install gulp-html-minifier-terser --save-dev
          # 压缩css
          npm install gulp-clean-css --save-dev
          # 压缩js
          npm install gulp-terser --save-dev
          # 压缩字体
          npm install gulp-fontmin --save-dev
          
          # sitemap
          npm install hexo-generator-sitemap --save #谷歌
          npm install hexo-generator-baidu-sitemap --save #百度

          # 电子时钟
          npm install hexo-butterfly-clock-anzhiyu --save
          # 历史上的今天
          npm i hexo-history-calendar --save


      # hexo 编译生成网站
      - name: Generate files
        # 编译 markdown 文件
        run: |
          hexo clean
          hexo generate
          gulp
          hexo swpp
      
      #  修复文章最后更新时间
      - name: Git Configuration  ## git配置信息，避免所有的文件发布日期都改变了
        run: |
          git config --global core.quotePath false
          git config --global core.autocrlf false
          git config --global core.safecrlf true
          git config --global core.ignorecase false  

      # 自动发布到当前仓库的gh-pages分支，如需部署到其它仓库的，参考下面文档修改配置即可
      # 更多高级用法查看文档，https://github.com/peaceiris/actions-gh-pages
      - name: Deploy
        uses: peaceiris/actions-gh-pages@v3
        with:
          # 1.如何生成 github_token，github主页,Settings / Developer settings / Personal access tokens (classic)
          # 2.github_token 在 仓库 Settings,Actions secrets and variables,设置
          # github_token: ${{ secrets.ACCESS_TOKEN }} 无需自定义 
          # GITHUB_TOKEN secret to authenticate in your workflow. So, you can start to deploy immediately without any configuration.
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./public

          # Deploy to external repository external_repository
          # 发布到其他仓库的配置，注意必须使用deploy_key，获取和设置方法同上。
          #deploy_key: ${{ secrets.ACTIONS_DEPLOY_KEY }}
          #external_repository: username/external-repository
          #publish_branch: your-branch  # default: gh-pages
          #publish_dir: ./public

          # 以下配置可忽略，测试功能，设置提交信息相关配置
          user_name: 'github-actions[bot]'
          user_email: 'github-actions[bot]@users.noreply.github.com'
          commit_message: ${{ github.event.head_commit.message }}
          #full_commit_message: ${{ github.event.head_commit.message }}
          tag_name: ${{ steps.prepare_tag.outputs.DEPLOY_TAG_NAME }}
          tag_message: 'Deployment ${{ github.ref_name }}'

